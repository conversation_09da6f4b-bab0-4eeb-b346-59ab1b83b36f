name: Quartermaster Python Microservices CI

on:
  push:
    branches: [ "main" ]
    paths:
      - "python/**"
      - ".github/workflows/Quartermaster-Python-Micro-Services-CI.yml"

jobs:
  build:
    runs-on: self-hosted
    permissions:
      contents: read
      actions: read
      statuses: write

    strategy:
      matrix:
        python-version: ["3.10.x"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        cache: pip
        cache-dependency-path: python/requirements.txt

##############################################################################################
############################## Quartermaster Python MicroServices CI ########################
##############################################################################################

    - name: Deploy to microservices.quartermaster.us - Navigate to Directory
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying Python microservice to microservices.quartermaster.us"
        cd ~/quartermaster-web-microservices/
        git restore .
        git pull

    - name: Deploy to microservices.quartermaster.us - Setup Python venv & Install Dependencies
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-web-microservices/python/
        if [ ! -d "venv" ]; then
          python3 -m venv venv
        fi
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt

    - name: Deploy to microservices.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-web-microservices/python/
        rm -f .env
        touch .env

    - name: Deploy to microservices.quartermaster.us - Populate .env File with Secrets
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-web-microservices/python/
        echo "${{ secrets.PY_MICROSERVICES_QUARTERMASTER_US }}" > .env

    - name: Deploy to microservices.quartermaster.us - Restart PM2 Process
      if: github.ref == 'refs/heads/main'
      run: |
        pm2 restart 'Quartermaster Webservices Python'

##############################################################################################
################################ Update Slack  ###############################################
##############################################################################################
    
    - name: Set URL based on branch
      run: |
        if [ "${{ github.ref }}" == "refs/heads/main" ]; then
          echo "URL=http://localhost:5010" >> $GITHUB_ENV
        else
          echo "URL=https://localhost:5010" >> $GITHUB_ENV
        fi

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nPython CI\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: '*********************************************************************************'

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nPython CI\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: '*********************************************************************************'