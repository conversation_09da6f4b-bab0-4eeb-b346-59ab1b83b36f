const mongoose = require('mongoose');
const db = require('../modules/db');

const aisMmsiLookupSchema = new mongoose.Schema({
    mmsi: { type: String, required: true, unique: true },
    last_message_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    last_message_timestamp: { type: Date, required: true },
    collection: { type: String, required: true },
    db: { type: String, required: true }
});

const AisMmsiLookup = db.lookups.model('AisMmsiLookup', aisMmsiLookupSchema, 'ais_mmsi_lookup');

module.exports = AisMmsiLookup