const ThingsboardDevices = require('../models/ThingsboardDevices');
const thingsboardApi = require('../modules/thingsboardApi');
const { createLoggerWithPath } = require('../modules/winston');

const logger = createLoggerWithPath('thingsboard');

class ThingsBoardService {
    async processSensorData(sensorData) {
        const { deviceName, telemetry } = sensorData;
        let device = await ThingsboardDevices.findOne({ deviceName });
        if (!device) return;

        try {
            await thingsboardApi.sendTelemetry(device.accessToken, telemetry);
        } catch (err) {
            logger.error(`[ThingsBoard] Failed to send telemetry for ${deviceName}: ${err.message}`);
        }
    }

    async verifySensors(sensors = []) {
        try {
            const results = await thingsboardApi.bulkCreateDevices(sensors);

            const created = results.filter(r => r.status === 'created').map(r => r.deviceName);
            const existing = results.filter(r => r.status === 'exists').map(r => r.deviceName);
            const failed = results.filter(r => r.status === 'error').map(r => r);

            if (created.length > 0) {
                logger.info(`[ThingsBoard] Created devices: ${created.join(", ")}`);
            }

            if (existing.length > 0) {
                logger.info(`[ThingsBoard] Existing devices: ${existing.join(", ")}`);
            }

            if (failed.length > 0) {
                failed.forEach(f => {
                    logger.error(`[ThingsBoard] Failed to create device: ${f.deviceName} with error: ${f.error}`);
                });
            }

            return results;
        } catch (err) {
            logger.error(`[ThingsBoard] Error in bulk device verification: ${err.message}`);
        }
    }
}

module.exports = new ThingsBoardService();