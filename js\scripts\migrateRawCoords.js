require('dotenv').config()
const db = require("../modules/db");
const { getTerminalConfirmation, getTerminalInput } = require('../utils/functions');

const getCollectionNames = async () => {
    return (await db.qmLocations.listCollections()).map(c => c.name).filter(c => !c.startsWith('system.'))
}

const updateCoords = (coords) => {
    return coords.map(c => {
        const { onboardVesselId, latitude, longitude, unitId, metadata, ...rest } = c;

        return {
            ...rest,
            location: {
                type: "Point",
                coordinates: [longitude, latitude]
            },
            metadata: {
                onboardVesselId,
                unitId
            },
            details: metadata || {}
        };
    });
};

const getMonthRange = (month) => {
    const [year, monthNum] = month.split('-').map(Number);
    const startDate = new Date(Date.UTC(year, monthNum - 1, 1, 0, 0, 0, 0));
    const endDate = new Date(Date.UTC(year, monthNum, 0, 23, 59, 59, 999));
    return [startDate, endDate];
}

async function optimizeCoords() {
    const month = await getTerminalInput('Enter month (YYYY-MM): ')
    const dateRange = getMonthRange(month)
    console.log('Date Range', dateRange)
    console.log('Using Source DB', db.qmLocations.name)
    const collectionNames = await getCollectionNames()
    console.log('collectionNames.length', collectionNames.length)

    if (!(await db.locationsRaw.listCollections({ name: month })).find(c => c.name === month)) {
        console.error(`Raw collection ${month} does not exist in DB ${db.locationsRaw.name}. You need to create it first before running this script`)
        process.exit(1)
    }

    console.log('fetching coordinates...')
    let allCoords = (await Promise.all(collectionNames.map(async cName => {
        const unitId = cName.split('_location')[0]
        const collection = db.qmLocations.collection(cName)
        const coords = (await collection.find({
            timestamp: {
                $gte: dateRange[0],
                $lte: dateRange[1]
            }
        }).toArray()).map(c => {
            return {
                ...c,
                unitId
            }
        })
        return coords
    }))).flat().sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

    console.log('allCoords', allCoords.length)

    const updatedCoordsRaw = updateCoords(allCoords)

    // clean memory
    allCoords = null
    console.log('cleaned memory')

    const confirmation = await getTerminalConfirmation(`Deleting and inserting data in collection ${month} in db ${db.locationsRaw.name}, continue? (y/n): `)
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    const rawCollection = db.locationsRaw.collection(month)

    console.log('deleting data...')
    await rawCollection.deleteMany({})
    console.log('deleted data')

    console.log('inserting data...')
    await rawCollection.insertMany(updatedCoordsRaw)
    console.log('inserted data')

    console.log('Success')
    process.exit(0)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qmLocations.once('open', resolve);
        db.qmLocations.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.locationsOptimized.once('open', resolve);
        db.locationsOptimized.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.locationsRaw.once('open', resolve);
        db.locationsRaw.on('error', reject);
    })
]).then(() => {
    setTimeout(() => {
        optimizeCoords()
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});