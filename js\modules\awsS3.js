const AWS = require('aws-sdk');
const sharp = require("sharp");

const s3 = new AWS.S3({
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
});

async function headObject(args){ return s3.headObject(args).promise()}

async function getObject(bucketName, region, key, sync = false) {
    s3.config.update({ region });
    const params = {
        Bucket: bucketName,
        Key: key,
    };

    try {
        const obj = s3.getObject(params);
        return sync ? obj.promise() : obj; // No await here
    } catch (err) {
        console.error("Error getting object from S3:", err);
        throw err; // Re-throw to be handled by caller
    }
}

async function buildThumbnailVideo(bucketName, region, key, unitName) {
    // try {
    //     // resize the video
    //     // const resizedBuffer =
    //
    //     const keyParts = key.split("/");
    //     const name = keyParts.pop();
    //     const uploadKey = `videos/${unitName}/${name}`;
    //
    //     const putObjectParams = {
    //         Bucket: process.env.AWS_COMPRESSED_ITEMS_BUCKET,
    //         Key: uploadKey,
    //         Body: resizedBuffer,
    //         ContentType: "video/mp4",
    //     };
    //
    //
    //     const uploadResult = await s3.putObject(putObjectParams).promise();
    //     if (uploadResult.ETag?.length > 0) {
    //         return uploadKey;
    //     } else {
    //         return undefined;
    //     }
    // } catch (error) {
    //     console.error("Error processing image:", error);
    //     throw error;
    // }
}

async function buildThumbnailImage(bucketName, region, key, unitName) {
    try {
        const originalBuffer = (await getObject(bucketName, region, key, true)).Body;
        const resizedBuffer = await sharp(originalBuffer).resize(null, 320).jpeg({ quality: 100 }).toBuffer();

        const keyParts = key.split("/");
        const name = keyParts.pop();
        const uploadKey = `images/${unitName}/${name}`;

        s3.config.update({ region: process.env.AWS_COMPRESSED_ITEMS_REGION });
        const putObjectParams = {
            Bucket: process.env.AWS_COMPRESSED_ITEMS_BUCKET,
            Key: uploadKey,
            Body: resizedBuffer,
            ContentType: "image/jpeg",
        };


        const uploadResult = await s3.putObject(putObjectParams).promise();
        if (uploadResult.ETag?.length > 0) {
            return uploadKey;
        } else {
            return undefined;
        }
    } catch (error) {
        console.error("Error processing image:", error);
        throw error;
    }
}

module.exports = {
    s3,
    buildThumbnailImage,
    headObject,
}