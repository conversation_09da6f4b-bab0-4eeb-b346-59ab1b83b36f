

const artifacts_mock_data = [
  {
    _id: "681c0b711f8711a72d1d8a1e",
    timestamp: "2025-05-08T01:32:34.058Z",
    bucket_name: "smartmast-prototype-24-ap",
    aws_region: "ap-southeast-1",
    unit_id: "prototype-24",
    onboard_vessel_name: "BR<PERSON>ones (MRRV-4404)",
    metadata_path: "artifacts/2025-05-02T08:59:35.132Z/image/prototype-24_cam-1_2025-05-08T01:32:34.058Z.json",
    image_path: "artifacts/2025-05-02T08:59:35.132Z/image/prototype-24_cam-1_2025-05-08T01:32:34.058Z.jpg",
    det_conf: 0.9292,
    det_nbbox: {
      x1: 0.58148,
      y1: 0.43897,
      x2: 0.7879,
      y2: 0.56059
    },
    det_nbbox_area: 0.025104800400000005,
    camera: null,
    recording_triggers: null,
    location: {
      type: "Point",
      coordinates: [
        125.43879609999999,
        5.0194171999999995
      ]
    },
    vessel_presence: true,
    category: "Patrol Corvette (Naval Warship)",
    super_category: "Military",
    text_extraction: [
      {
        text: "849",
        confidence: 0.98
      }
    ],
    imo_number: null,
    color: "Gray",
    size: "Medium",
    weapons: "Visible gun turret on the deck",
    country_flag: null,
    others: "The vessel is identified as a naval corvette or offshore patrol vessel (OPV), based on hull number '849' and visible 'PAK' marking.",
    artifact_processor_version: "0.5.0",
    thumbnail_image_path: "images/prototype-24/prototype-24_cam-1_2025-05-08T01:32:34.058Z.jpg",
    true_bearing: null,
    onboard_vessel_id: "683df46a073245cf0fd62bb5"
  },
  {
    _id: "681c097e1f8711a72d1d8a19",
    timestamp: "2025-05-08T01:23:43.694Z",
    bucket_name: "smartmast-prototype-24-ap",
    aws_region: "ap-southeast-1",
    unit_id: "prototype-24",
    onboard_vessel_name: "BRP Capones (MRRV-4404)",
    metadata_path: "artifacts/2025-05-02T08:59:35.132Z/image/prototype-24_cam-1_2025-05-08T01:23:43.694Z.json",
    image_path: "artifacts/2025-05-02T08:59:35.132Z/image/prototype-24_cam-1_2025-05-08T01:23:43.694Z.jpg",
    det_conf: 0.93408,
    det_nbbox: {
      x1: 0.31285,
      y1: 0.4679,
      x2: 0.57625,
      y2: 0.6254
    },
    det_nbbox_area: 0.041485499999999995,
    camera: null,
    recording_triggers: null,
    location: {
      type: "Point",
      coordinates: [
        125.4179147,
        5.0230713
      ]
    },
    vessel_presence: true,
    category: "Patrol Boat",
    super_category: "Military",
    text_extraction: [
      {
        text: "849",
        confidence: 0.98
      },
      {
        text: "PARI",
        confidence: 0.95
      }
    ],
    imo_number: null,
    color: "Dark grey",
    size: "medium",
    weapons: "Visible gun turret on the deck",
    country_flag: "Indonesia",
    others: "An Indonesian Navy fast patrol boat. It features a stealth-inspired angular hull design, forward gun mount, and radar dome.",
    artifact_processor_version: "0.5.0",
    thumbnail_image_path: "images/prototype-24/prototype-24_cam-1_2025-05-08T01:23:43.694Z.jpg",
    true_bearing: null,
    onboard_vessel_id: "683df46a073245cf0fd62bb5"
  }


];

const testMode_mock_data = {
  mapUrl: "https://portal.quartermaster.us/api/notificationsAlerts/map?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbnMiOltdLCJ0aW1lc3RhbXAiOjE3NDQyOTgzNzUyMjMsImlhdCI6MTc0NDI5ODM3NSwiZXhwIjoxNzU5ODUwMzc1fQ.cBdo37KY3NlyvAr7l1QHJ4g-D503JwTNROqHIG4Mcq0"
}



module.exports = {
  artifacts_mock_data,
  testMode_mock_data
}