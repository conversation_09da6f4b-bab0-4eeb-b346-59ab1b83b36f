require('dotenv').config()
const cron = require('node-cron');
const db = require("../modules/db");
const simplify = require('simplify-js');

const getSimplifiedCoords = (coords) => {
    // Step 1: Prepare points with a reference index
    const points = coords.map((c, i) => ({
        x: c.longitude,
        y: c.latitude,
        _index: i // store original index
    }));

    // Step 2: Simplify
    const simplified = simplify(points, 0.0001, true);

    // Step 3: Map back to original objects
    const result = simplified.map(p => coords[p._index]);

    return result;
}

const updateCoords = (coords) => {
    return coords.map(c => {
        const { onboardVesselId, latitude, longitude, unitId, metadata, ...rest } = c;

        return {
            ...rest,
            location: {
                type: "Point",
                coordinates: [longitude, latitude]
            },
            metadata: {
                onboardVesselId,
                unitId
            },
            details: metadata || {}
        };
    });
};

const getPreviousMonthRange = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth(); // 0-based month
    
    // Get previous month
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevYear = month === 0 ? year - 1 : year;
    
    const startDate = new Date(Date.UTC(prevYear, prevMonth, 1, 0, 0, 0, 0));
    const endDate = new Date(Date.UTC(prevYear, prevMonth + 1, 0, 23, 59, 59, 999));
    
    // Format as YYYY-MM for collection name
    const monthString = `${prevYear}-${String(prevMonth + 1).padStart(2, '0')}`;
    
    return { startDate, endDate, monthString };
}

async function optimizeCoordsForPreviousMonth() {
    try {
        console.log('Starting monthly coordinate optimization...');
        
        const { startDate, endDate, monthString } = getPreviousMonthRange();
        console.log(`Processing month: ${monthString}`);
        console.log('Date Range:', startDate, 'to', endDate);
        
        // Check if optimized collection already exists
        if ((await db.locationsOptimized.listCollections({ name: monthString })).find(c => c.name === monthString)) {
            console.log(`Optimized collection ${monthString} already exists, dropping it...`);
            await db.locationsOptimized.collection(monthString).drop();
        }

        // Check if raw collection exists (it should already exist)
        const rawCollectionExists = (await db.locationsRaw.listCollections({ name: monthString })).find(c => c.name === monthString);
        if (!rawCollectionExists) {
            console.error(`Raw collection ${monthString} does not exist. Cannot proceed with optimization.`);
            return;
        }

        console.log('Fetching coordinates from raw collection...');
        
        // Fetch coordinates from raw collection instead of locations db
        const rawCollection = db.locationsRaw.collection(monthString);
        const allCoords = await rawCollection.find({
            timestamp: {
                $gte: startDate,
                $lte: endDate
            }
        }).toArray();

        console.log('Raw coordinates found:', allCoords.length);

        if (allCoords.length === 0) {
            console.log('No coordinates found for the specified month range.');
            return;
        }

        // Sort by timestamp
        allCoords.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

        const distinctVesselIds = [...new Set(allCoords.map(c => c.metadata?.onboardVesselId?.toString()))].filter(vesselId => vesselId);
        console.log('Distinct vessel IDs:', distinctVesselIds.length);

        let optCoords = [];
        distinctVesselIds.forEach(vesselId => {
            const coords = allCoords.filter(c => c.metadata?.onboardVesselId?.toString() === vesselId)
                .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            
            // Convert to format expected by getSimplifiedCoords
            const coordsForSimplification = coords.map(c => ({
                ...c,
                longitude: c.location.coordinates[0],
                latitude: c.location.coordinates[1],
                onboardVesselId: c.metadata.onboardVesselId,
                unitId: c.metadata.unitId
            }));
            
            const optimizedCoords = getSimplifiedCoords(coordsForSimplification);
            console.log(`Vessel ${vesselId}: optimized ${optimizedCoords.length} from ${coords.length} coordinates`);
            optCoords.push(...optimizedCoords);
        });

        console.log('Total optimized coordinates:', optCoords.length, 'vs original:', allCoords.length);

        const updatedCoords = updateCoords(optCoords);

        // Create optimized collection
        console.log(`Creating optimized collection ${monthString}...`);
        const optimizedCollection = await db.locationsOptimized.createCollection(monthString, { 
            timeseries: { 
                timeField: 'timestamp', 
                metaField: 'metadata', 
                granularity: 'seconds' 
            } 
        });

        // Create indexes
        await optimizedCollection.createIndex({ timestamp: 1, 'metadata.onboardVesselId': 1 });
        await optimizedCollection.createIndex({ 'metadata._id': 1 });
        await optimizedCollection.createIndex({ 'metadata.onboardVesselId': 1 });

        // Insert optimized coordinates
        await optimizedCollection.insertMany(updatedCoords);

        console.log(`Successfully created optimized collection ${monthString} with ${updatedCoords.length} records`);
        
    } catch (error) {
        console.error('Error in monthly coordinate optimization:', error);
    }
}

// Schedule cron job to run at 00:00 on the 1st day of every month
const scheduleCronJob = () => {
    console.log('Scheduling monthly coordinate optimization cron job...');
    
    // Run at 00:00 on the 1st day of every month
    cron.schedule('0 0 1 * *', async () => {
        console.log('Monthly coordinate optimization cron job triggered');
        await optimizeCoordsForPreviousMonth();
    }, {
        scheduled: true,
        timezone: "UTC"
    });
    
    console.log('Monthly coordinate optimization cron job scheduled successfully');
};

// Wait for database connections before scheduling
Promise.all([
    new Promise((resolve, reject) => {
        db.locationsOptimized.once('open', resolve);
        db.locationsOptimized.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.locationsRaw.once('open', resolve);
        db.locationsRaw.on('error', reject);
    })
]).then(() => {
    scheduleCronJob();
}).catch(err => {
    console.error('Failed to establish database connections for coordinate optimization scheduler:', err);
});

module.exports = { optimizeCoordsForPreviousMonth, scheduleCronJob };
