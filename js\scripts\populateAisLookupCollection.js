require('dotenv').config();
const AisMmsiLookup = require('../models/AisMmsiLookup');
const db = require('../modules/db');
const { getAisCollectionNames, getTerminalConfirmation } = require('../utils/functions');

async function populateAisLookupCollection() {
    const aisCollections = await getAisCollectionNames(db.qmAis)
    console.log('aisCollections', aisCollections)

    const all_messages = (await Promise.all(aisCollections.map(async collection =>
        (await db.qmAis.collection(collection).find().toArray()).map(message => {
            return {
                ...message,
                collection: collection,
                db: db.qmAis.name
            }
        })
    ))).flat()

    console.log('all_messages', all_messages.length)

    const uniqueLatestAis = {}

    for (const message of all_messages) {
        if (!uniqueLatestAis[message.mmsi]) {
            uniqueLatestAis[message.mmsi] = message
        } else {
            if (new Date(message.timestamp) > new Date(uniqueLatestAis[message.mmsi].timestamp)) {
                uniqueLatestAis[message.mmsi] = message
            }
        }
    }

    // take confirmation from user in ternminal
    const confirmation = await getTerminalConfirmation(`Upserting ${Object.values(uniqueLatestAis).length} messages in environment ${process.env.NODE_ENV}, continue? (y/n): `);

    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    const confirmWashDB = await getTerminalConfirmation(`Do you wish to clean the AIS lookup collection? (y/n): `);

    if (confirmWashDB) {
        await AisMmsiLookup.deleteMany({});
        console.log('AIS lookup collection cleaned');
    }

    await Promise.all(Object.values(uniqueLatestAis).map(async message => {
        await AisMmsiLookup.findOneAndUpdate({ mmsi: message.mmsi }, {
            $set: {
                last_message_id: message._id,
                last_message_timestamp: message.timestamp,
                db: message.db,
                collection: message.collection
            }
        }, { upsert: true })
    }))

    console.log('Success')
    process.exit(0)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qmAis.once('open', resolve);
        db.qmAis.on('error', reject);
    })
]).then(() => {
    populateAisLookupCollection()
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});