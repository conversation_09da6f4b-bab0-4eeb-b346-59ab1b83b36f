const mongoose = require("mongoose");
const db = require("../modules/db");
const { defaultDateTimeFormat } = require("../utils/functions");
const Role = require("./Role");
const { permissions } = require("../utils/permissions");

const userSchema = new mongoose.Schema({
    name: { type: String, required: true },
    email: { type: String },
    username: { type: String },
    date_time_format: { type: String, required: false, default: defaultDateTimeFormat },
    use_MGRS: { type: Boolean, required: false, default: false },
    password: { type: String, required: true },
    jwt_tokens: { type: Array, required: false, default: [] },
    reset_password_token: { type: String, required: false, default: null },
    reset_password_expire: { type: Number, required: false, default: null },
    email_verification_enabled: { type: Boolean, required: true, default: false },
    email_verified_device_ids: { type: Array, required: false, default: [] },
    role_id: { type: Number, required: true },
    deletable: { type: Boolean, required: true, default: true },
    is_deleted: { type: Boolean, required: true, default: false },
    allowed_vessels: { type: Array, required: true },
    organization_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

userSchema.virtual('permissions').get(async function() {
    if (this.role_id == null) {
        return []; // No role, no permissions
    }

    try {
        const role = await Role.findOne({ role_id: this.role_id }).lean(); // .lean() for performance if not modifying role

        if (!role) {
            console.warn(`Role with role_id ${this.role_id} not found for user ${this._id}`);
            return []; // Role not found, no permissions
        }

        const deniedPermissions = role.denied_permissions || [];
        const calculatedPermissions = [];

        for (const permissionKey in permissions) {
            const permissionId = permissions[permissionKey];
            if (!deniedPermissions.includes(permissionId)) {
                calculatedPermissions.push({ permission_id: permissionId });
            }
        }
        return calculatedPermissions;
    } catch (error) {
        console.error("Error fetching permissions for user:", error);
        return []; // Return empty on error to prevent crashes, or handle error differently
    }
});

// To include virtuals when converting a document to JSON or an Object
userSchema.set('toJSON', { virtuals: true });
userSchema.set('toObject', { virtuals: true });

const User = db.qm.model("User", userSchema);

module.exports = User;
