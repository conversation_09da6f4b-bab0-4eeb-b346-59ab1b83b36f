const axios = require('axios');

const axiosInstance = axios.create({
    baseURL: process.env.FLASK_API_URL,
    headers: { 'Content-Type': 'application/json' }
});

class EvaluatorApi {
    async evaluateSingle(prevArtifact, currArtifact) {
        try {
            console.log(`[EvaluatorApi] Evaluating single pair: ${prevArtifact._id} vs ${currArtifact._id}`);
            
            const response = await axiosInstance.post('/evaluate/single', {
                prev_artifact: prevArtifact,
                curr_artifact: currArtifact
            });

            if (response.data && response.data.duplication_index !== undefined) {
                const duplicationIndex = response.data.duplication_index;
                console.log(`[EvaluatorApi] Single evaluation result: duplication_index=${duplicationIndex}`);
                return duplicationIndex;
            } else {
                console.log(`[EvaluatorApi] No evaluation result from single API`);
                return null;
            }
        } catch (error) {
            console.error(`[<PERSON>luatorApi] Error in single evaluation:`, error.message);
            return null;
        }
    }

    async evaluateMultiple(sortedArtifacts) {
        try {
            console.log(`[EvaluatorApi] Evaluating multiple artifacts: ${sortedArtifacts.length} artifacts`);
            
            const response = await axiosInstance.post('/evaluate/multiple', {
                sorted_artifacts: sortedArtifacts
            });

            if (response.data && Array.isArray(response.data)) {
                console.log(`[EvaluatorApi] Multiple evaluation results: ${response.data.length} results`);
                return response.data;
            } else {
                console.log(`[EvaluatorApi] No evaluation results from multiple API`);
                return null;
            }
        } catch (error) {
            console.error(`[EvaluatorApi] Error in multiple evaluation:`, error.message);
            return null;
        }
    }
}

module.exports = new EvaluatorApi();
