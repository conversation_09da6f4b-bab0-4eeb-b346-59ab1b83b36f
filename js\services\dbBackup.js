/**
 * This script requires installation of MongoDB Command Line Database Tools
 * https://www.mongodb.com/try/download/database-tools
 */

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const archiver = require('archiver');
const cron = require('node-cron');
const { s3 } = require('../modules/awsS3');
const os = require('os');
const { postLogToSlack } = require('../modules/notifyLog');

// Resetting pathname to remove the trailing database name if exists
const url = new URL(process.env.MONGO_URI);
url.pathname = '/'
const CONNECTION_URL = url.toString();
const DB_NAMES = ['quartermaster', 'artifact_processor', 'locations', 'quartermaster-shared', 'quartermaster-dev'];
const S3_BUCKET = 'quartermaster-web-application-backups';

// Schedule backup to run daily at midnight
cron.schedule('0 0 * * *', () => {
    console.log('DB backup cron job initialized');
    backupMongoDB();
});

async function backupMongoDB() {
    console.log('Initiating backups for databases', DB_NAMES.join(', '));
    const ts = new Date().getTime();
    const timestamp = new Date().toISOString().replace(/[:.-]/g, '_');
    const tempDir = path.join(os.tmpdir(), `backup_${timestamp}`);

    if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
    }

    try {
        await Promise.all(DB_NAMES.map(async (dbName) => {
            const command = `mongodump --uri "${CONNECTION_URL}" --db ${dbName} --out "${tempDir}"`;
            await new Promise((resolve, reject) => {
                exec(command, (error) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    console.log(`Backup for ${dbName} completed successfully! Files saved in: ${tempDir}`);
                    resolve();
                });
            });

            // Create zip file
            const zipPath = path.join(tempDir, `${dbName}.zip`);
            await zipBackup(`${tempDir}/${dbName}`, zipPath);

            // Upload to S3
            const s3Key = `backups/${timestamp}/${dbName}.zip`;
            await uploadToS3(zipPath, s3Key);

            // Clean up the unzipped backup
            fs.rmSync(`${tempDir}/${dbName}`, { recursive: true, force: true });
        }));

        // Clean up temp directory after all backups are done
        fs.rmSync(tempDir, { recursive: true, force: true });
        console.log('All backups completed and uploaded to S3');

        postLogToSlack({
            severity: 'info',
            message: `Successfully backed up ${DB_NAMES.join(', ')} databases\nTime taken: ${new Date().getTime() - ts} ms`,
            stack: 'N/A'
        });
    } catch (error) {
        console.error('Error during backup process:', error);
        postLogToSlack({
            severity: 'fatal',
            message: 'Error during database backup',
            stack: error.stack
        });
        // Clean up temp directory in case of error
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    }
}

function zipBackup(dir, outputPath) {
    return new Promise((resolve, reject) => {
        const output = fs.createWriteStream(outputPath);
        const archive = archiver('zip', {
            zlib: { level: 9 }, // Set compression level
        });

        output.on('close', () => {
            console.log(`Zipped backup created at: ${outputPath}. Total size: ${archive.pointer() / 1048576} MB`);
            resolve();
        });

        archive.on('error', (err) => {
            reject(err);
        });

        archive.pipe(output);
        archive.directory(dir + '/', false);
        archive.finalize();
    });
}

async function uploadToS3(filePath, key) {
    try {
        const fileStream = fs.createReadStream(filePath);
        const params = {
            Bucket: S3_BUCKET,
            Key: key,
            Body: fileStream
        };

        await s3.upload(params).promise();
        console.log(`Successfully uploaded backup to S3: ${key}`);
    } catch (error) {
        console.error(`Error uploading to S3: ${error}`);
        throw error;
    }
}