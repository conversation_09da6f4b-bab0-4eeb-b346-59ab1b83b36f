
const { createLoggerWithPath } = require('../modules/winston');
const io = require('../modules/io');
const { getVesselIdbyUnitId } = require('../utils/functions');
const { getAisCollection } = require('../models/VesselAis');
const AisMmsiLookup = require('../models/AisMmsiLookup');
const mongoose = require('mongoose');

async function processIotAisMessage(topic, message, region) {
    if (!topic.endsWith('/sdr/status')) return;

    const unit_id = topic.split('/').shift();

    const logger = createLoggerWithPath(`mqtt/ais/${region}/${unit_id}`);

    try {
        const decoder = new TextDecoder('utf-8');
        const messageString = decoder.decode(message);
        const data = JSON.parse(messageString);

        const { ais_messages } = data;

        const onboardVesselId = await getVesselIdbyUnitId(unit_id);

        for (const message of ais_messages) {

            var { nav_latitude: latitude, nav_longitude: longitude, mmsi, name } = message;

            if (!mmsi) return logger.error(`[AIS Service] Invalid AIS message for unit ${unit_id} in Region ${region}: ${JSON.stringify(message)}: mmsi is ${mmsi}`);
            // if (!name) return logger.error(`[AIS Service] Invalid AIS message for unit ${unit_id} in Region ${region}: ${JSON.stringify(message)}: name is ${name}`);
            if (!latitude || !longitude) return logger.error(`[AIS Service] Invalid AIS message for unit ${unit_id} in Region ${region}: ${JSON.stringify(message)}: latitude is ${latitude}, longitude is ${longitude}`);

            logger.info(`[AIS Service] Received AIS for unit ${unit_id} in Region ${region}: Latitude = ${latitude}, Longitude = ${longitude}`);

            const objId = new mongoose.Types.ObjectId();

            const structuredData = {
                _id: objId,
                onboard_vessel_id: onboardVesselId,
                location: {
                    type: 'Point',
                    coordinates: [longitude, latitude]
                },
                mmsi,
                name: name || null,
                timestamp: new Date(message.timestamp),
                metadata: {
                    ...data,
                    message,
                    ais_messages: null,
                    _id: objId
                }
            }

            io.emit(`${unit_id}/ais`, structuredData);

            const collection = getAisCollection(unit_id);
            if (!collection) return logger.error(`[AIS Service] Unable to find collection for ${unit_id}`);

            const dbRecord = await collection.create(structuredData)

            await AisMmsiLookup.findOneAndUpdate({ mmsi }, {
                $set: {
                    last_message_id: dbRecord._id,
                    last_message_timestamp: structuredData.timestamp,
                    db: collection.db.name,
                    collection: collection.collection.name
                }
            }, { upsert: true })

            io.emit(`${unit_id}_ais/insert`, structuredData)

        }
    } catch (error) {
        logger.error(`[AIS Service] Error processing MQTT message for Vessel ${unit_id} in Region ${region}`, JSON.stringify(error));
        throw error;
    }
}

module.exports = {
    processIotAisMessage
}