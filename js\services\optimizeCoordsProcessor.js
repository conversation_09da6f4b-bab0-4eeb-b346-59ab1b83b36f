const db = require("../modules/db");
const simplify = require('simplify-js');
const { createLoggerWithPath } = require('../modules/winston');
const { schedule } = require("node-cron");
const { postLogToSlack } = require('../modules/notifyLog');

const logger = createLoggerWithPath('optimize_coords_scheduler');

const getSimplifiedCoords = (coords) => {
    // Step 1: Prepare points with a reference index
    const points = coords.map((c, i) => ({
        x: c.longitude,
        y: c.latitude,
        _index: i // store original index
    }));

    // Step 2: Simplify
    const simplified = simplify(points, 0.0001, true);

    // Step 3: Map back to original objects
    const result = simplified.map(p => coords[p._index]);

    return result;
}

const updateCoords = (coords) => {
    return coords.map(c => {
        const { onboardVesselId, latitude, longitude, unitId, metadata, ...rest } = c;

        return {
            ...rest,
            location: {
                type: "Point",
                coordinates: [longitude, latitude]
            },
            metadata: {
                onboardVesselId,
                unitId
            },
            details: metadata || {}
        };
    });
};

const getPreviousMonthRange = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth(); // 0-based month
    
    // Get previous month
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevYear = month === 0 ? year - 1 : year;
    
    const startDate = new Date(Date.UTC(prevYear, prevMonth, 1, 0, 0, 0, 0));
    const endDate = new Date(Date.UTC(prevYear, prevMonth + 1, 0, 23, 59, 59, 999));
    
    // Format as YYYY-MM for collection name
    const monthString = `${prevYear}-${String(prevMonth + 1).padStart(2, '0')}`;
    
    return { startDate, endDate, monthString };
}

async function optimizeCoordsProcessor() {
    try {
        const ts = new Date().getTime();

        logger.info('[optimizeCoordsProcessor] invoked');

        const { startDate, endDate, monthString } = getPreviousMonthRange();
        logger.info(`Processing month: ${monthString}`);
        logger.info(`Date Range: ${startDate} to ${endDate}`);

        // Check if optimized collection already exists - should throw fatal error
        if ((await db.locationsOptimized.listCollections({ name: monthString })).find(c => c.name === monthString)) {
            const errorMessage = `Optimized collection ${monthString} already exists. This should not happen in scheduled processing.`;
            logger.error(errorMessage);
            postLogToSlack({
                severity: 'fatal',
                message: errorMessage,
                stack: 'N/A'
            });
            throw new Error(errorMessage);
        }

        // Check if raw collection exists - should throw fatal error if not found
        const rawCollectionExists = (await db.locationsRaw.listCollections({ name: monthString })).find(c => c.name === monthString);
        if (!rawCollectionExists) {
            const errorMessage = `Raw collection ${monthString} does not exist. Cannot proceed with optimization.`;
            logger.error(errorMessage);
            postLogToSlack({
                severity: 'fatal',
                message: errorMessage,
                stack: 'N/A'
            });
            throw new Error(errorMessage);
        }

        logger.info('Fetching coordinates from raw collection...');

        // Fetch coordinates from raw collection
        const rawCollection = db.locationsRaw.collection(monthString);
        const allCoords = await rawCollection.find({
            timestamp: {
                $gte: startDate,
                $lte: endDate
            }
        }).toArray();

        logger.info(`Raw coordinates found: ${allCoords.length}`);
        logger.info(`Time taken to fetch data: ${new Date().getTime() - ts}ms`);

        if (allCoords.length === 0) {
            logger.info('No coordinates found for the specified month range.');
            return;
        }

        // Sort by timestamp
        allCoords.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

        const distinctVesselIds = [...new Set(allCoords.map(c => c.metadata?.onboardVesselId?.toString()))].filter(vesselId => vesselId);
        logger.info(`Distinct vessel IDs: ${distinctVesselIds.length}`);

        let optCoords = [];
        distinctVesselIds.forEach(vesselId => {
            const coords = allCoords.filter(c => c.metadata?.onboardVesselId?.toString() === vesselId)
                .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

            // Convert to format expected by getSimplifiedCoords
            const coordsForSimplification = coords.map(c => ({
                ...c,
                longitude: c.location.coordinates[0],
                latitude: c.location.coordinates[1],
                onboardVesselId: c.metadata.onboardVesselId,
                unitId: c.metadata.unitId
            }));

            const optimizedCoords = getSimplifiedCoords(coordsForSimplification);
            logger.info(`Vessel ${vesselId}: optimized ${optimizedCoords.length} from ${coords.length} coordinates`);
            optCoords.push(...optimizedCoords);
        });

        logger.info(`Total optimized coordinates: ${optCoords.length} vs original: ${allCoords.length}`);

        const updatedCoords = updateCoords(optCoords);

        // Create optimized collection
        logger.info(`Creating optimized collection ${monthString}...`);
        const optimizedCollection = await db.locationsOptimized.createCollection(monthString, {
            timeseries: {
                timeField: 'timestamp',
                metaField: 'metadata',
                granularity: 'seconds'
            }
        });

        // Create indexes
        await optimizedCollection.createIndex({ timestamp: 1, 'metadata.onboardVesselId': 1 });
        await optimizedCollection.createIndex({ 'metadata._id': 1 });
        await optimizedCollection.createIndex({ 'metadata.onboardVesselId': 1 });

        // Insert optimized coordinates
        await optimizedCollection.insertMany(updatedCoords);

        logger.info(`[optimizeCoordsProcessor] saved to db`);
        logger.info(`Successfully created optimized collection ${monthString} with ${updatedCoords.length} records`);

        postLogToSlack({
            severity: 'info',
            message: `Monthly coordinate optimization completed successfully for ${monthString}\nTime taken: ${new Date().getTime() - ts}ms\nOptimized: ${updatedCoords.length} from ${allCoords.length} coordinates`,
            stack: 'N/A'
        });

    } catch (error) {
        logger.error(`[optimizeCoordsProcessor] error: ${error.message}`);
        console.error('[optimizeCoordsProcessor] error:', error);
        postLogToSlack({
            severity: 'fatal',
            message: 'Monthly coordinate optimization failed',
            stack: error.stack
        });
    }
}

logger.info('setting init timer for monthly coordinate optimization');

// Schedule to run at 00:00 on the 1st day of every month
// schedule('0 0 1 * *', optimizeCoordsProcessor, { scheduled: true, timezone: 'UTC' });
optimizeCoordsProcessor()
