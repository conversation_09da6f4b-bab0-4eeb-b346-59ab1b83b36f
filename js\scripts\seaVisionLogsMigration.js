require('dotenv').config();
console.log(process.env.MONGO_URI)
const fs = require('fs');
const db = require('../modules/db');

const data = fs.readFileSync('scripts/temp/newSeaVisionLogs.json', 'utf8');

const logs = JSON.parse(data);

const docs = []

for (const log of logs) {
    delete log.__v;
    delete log._id;
    log.created_at = new Date(log.created_at)

    if (Array.isArray(log.submitted_data)) {
        log.endpoint = 'PostRFDetection'
    } else {
        log.endpoint = 'PostCameraDetection'
    }

    docs.push(log)
}

console.log('parsed docs', docs.length)

db.qm.once('open', () => {
    db.qm.collection('logs_sea_vision').insertMany(docs).then(res => {
        console.log('inserted', res.insertedCount)
    }).catch(err => {
        console.error('error', err)
    })
})