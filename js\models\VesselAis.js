const mongoose = require('mongoose');
const db = require("../modules/db");

let VesselAis = {}

const getAisCollection = (unit_id) => {
    const collection = `${unit_id}_ais`

    if (VesselAis[collection]) return VesselAis[collection]

    const VesselAisSchema = new mongoose.Schema({
        location: {
            type: {
                type: String,
                enum: ['Point'],
                required: true
            },
            coordinates: {
                type: [Number],
                required: true
            }
        },
        onboard_vessel_id: { type: mongoose.Schema.Types.ObjectId, required: false, default: null },
        mmsi: { type: String, required: true },
        name: { type: String, required: false },
        metadata: { type: Object, required: true },
        timestamp: { type: Date, required: true, index: true },
        creation_timestamp: { type: Date, required: false, default: () => new Date() }
    }, { minimize: false, timeseries: { timeField: 'timestamp', metaField: 'metadata', granularity: 'seconds' } });

    VesselAisSchema.index({ "metadata._id": 1 })

    VesselAis[collection] = db.qmAis.model(collection, VesselAisSchema, collection);

    return VesselAis[collection]
}

module.exports = {
    getAisCollection
};