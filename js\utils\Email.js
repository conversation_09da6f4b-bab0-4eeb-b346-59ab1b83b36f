const {buildUrl} = require("./functions");
const privacy = "https://quartermaster.us/";
const terms = "https://quartermaster.us/";
const logo = "https://portal.quartermaster.us/email/header.png";
const backgroundImage = "https://portal.quartermaster.us/email/ship.png";

// This is sample of aritfact object for artifact notification alert
// const sampleArtifactData = {
//     timestamp: "2023-10-01T12:00:00Z",
//     last_observed: "DDF3-45",
//     location: {
//         coordinates: [37.7749, -122.4194],
//     },
//     onboard_vessel_name: "Quartermaster Explorer",
//     hull_number: "QM-12345",
//     country_flag: "United States",
//     speed: "123",
//     heading: "North-East",
//     vms_supported: "Yes",
//     ais_supported: "Yes",
//     super_category: "Cargo",
//     sub_category: "Container Ship",
//     color: "Blue",
//     size: "Large",
//     description: "A large cargo vessel carrying containers.",
//     signedUrl: "https://farm4.staticflickr.com/3075/3168662394_7d7103de7d_z_d.jpg",
//     _id: "23m3242sdsfsdf43242",
// };

const ARTIFACT_NOTIFICATION_EMAIL_CONTENT = (artifact, coordinates, date, unsubscribeLink, flagIco, mapData = undefined) => {
    const printData = [
        [
            ['Date & Time', artifact.timestamp ? new Date(artifact.timestamp).toLocaleString() : 'N/A'],
            ['Vessel Last Observed', artifact.last_observed || 'N/A'],
        ],

        [
            ['Coordinates', coordinates || 'N/A'],
            ['Observing Smartmast Vessel', artifact?.onboard_vessel_name || 'N/A'],
        ],

        [
            ['Hull Number', artifact?.hull_number || 'N/A'],
            ['Vessel Flag', flagIco ? `${flagIco}${flagIco ? ' ' : ''}${artifact?.country_flag}` : 'N/A'],
        ],

        [
            ['Vessel Speed', artifact?.speed || 'N/A'],
            ['Target Vessel Heading', artifact?.heading || 'N/A'],
        ],

        [
            ['VMS Supported', artifact?.vms_supported || 'N/A'],
            ['AIS Supported', artifact?.ais_supported || 'N/A'],
        ],

        [
            ['Category', artifact?.super_category || 'Unspecified category'],
            ['Sub Category', artifact?.sub_category || 'N/A'],
        ],

        [
            ['Color', artifact?.color || 'N/A'],
            ['Size', artifact?.size || 'N/A'],
        ],

        [
            ['Description', artifact?.description || 'N/A'],
            ['', '']
        ],
    ];

    const allPairs = printData.flat();
    const validPairs = allPairs.filter((pair) => pair[1] && pair[1] !== 'N/A');

    // Group valid pairs two at a time
    let renderedRows = '';
    for (let i = 0; i < validPairs.length; i += 2) {
        if (i + 1 < validPairs.length) {
            renderedRows += `<tr>
                                <td class='heading_left'>${validPairs[i][0]}</td>
                                <td class="heading_right">${validPairs[i + 1][0]}</td>
                             </tr>
                             <tr>
                                <td class="data_left">${validPairs[i][1]}</td>
                                <td class="data_right">${validPairs[i + 1][1]}</td>
                             </tr>`;
        } else {
            renderedRows += `<tr>
                                <td class="heading_left">${validPairs[i][0]}</td>
                             </tr>
                             <tr>
                                <td class="data_left">${validPairs[i][1]}</td>
                             </tr>`;
        }
    }

    return `
        <!DOCTYPE html>
        <html lang="en">        
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        background-color: #C4C4C400;
                        margin: 0;
                        padding: 0;
                    }
            
                    table {
                        border-spacing: 0;
                        width: 100%;
                        margin: 0 auto;
                    }
            
                    .date {
                        color: black;
                        font-size: 16px;
                        font-weight: bold;
                        text-align: right
                    }
            
                    .content {
                        background: #FFFFFF;
                        border-radius: 8px;
                        padding: 20px;
                        width: 80%;
                        margin: 0 auto;
                        z-index: 2;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                    }
            
                    .content h1 {
                        color: #333;
                        font-size: 24px;
                        margin-bottom: 10px;
                        text-align: center;
                    }
            
                    .content p {
                        color: #666666;
                        font-size: 16px;
                        margin-bottom: 20px;
                        text-align: center;
                    }
            
                    .heading_left {
                        text-align: left;
                        font-weight: bold;
                        color: #0285FE;
                        width: 50%;
                        padding-top: 5px;
                        padding-bottom: 3px;
                        font-size: 20px;
                    }
            
                    .heading_right {
                        text-align: right;
                        font-weight: bold;
                        color: #0285FE;
                        width: 50%;
                        font-size: 20px;
                    }
            
                    .data_left {
                        text-align: left;
                        color: #1F1F1F;
                    }
            
                    .data_right {
                        text-align: right;
                        color: #1F1F1F;
                    }
            
                    .footer {
                        font-size: 12px;
                        color: #999;
                        text-align: center;
                        padding: 20px;
                    }
            
                    .footer p {
                        margin: 5px 0;
                    }
            
                    @media (max-width: 768px) {
                        .content {
                            width: 90%;
                        }
                        .content h1 {
                            font-size: 20px;
                        }
                        .content p {
                            font-size: 14px;
                        }
            
                        .heading_left {
                            font-size: 16px;
                        }
            
                        .heading_right {
                            font-size: 16px;
                        }
                        .footer {
                            font-size: 10px;
                        }
                        .date {
                            font-size: 14px;
                        }
                    }
                </style>
                <title>Artifact Notification</title>
            </head>
            
            <body>
                <table style="width: 100%;" cellpadding="0" cellspacing="0">
                    <!-- Top Section -->
                    <tr>
                        <td>
                            <img style="width:100% ; height:auto" src=${logo}>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <table class="content" style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">
            
                                <!-- Center Content -->
                                <tr>
                                    <td class="date">${date}</td>
                                </tr>
            
                                <tr>
                                    <td>
                                        <h1 style="color:#1F1F1F;">Detection Notification</h1>
                                    </td>
                                </tr>
            
                                <tr>
                                    <td>
                                        <table style="width: 80%">
                                            ${renderedRows}
                                        </table>
                                    </td>
                                </tr>
            
                                <tr>
                                    <td>
                                        <p> <a style="background-color:#1E293B; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                                " href=${process.env.APP_URL + '/dashboard/events/' + artifact._id}> View in Portal </a>
                                                                                        </td>
                                </tr>
            
                            
                                ${artifact?.signedUrl
            ? `<tr>
                                            <td style="text-align:center">
                                                <img src=${buildUrl(`/artifacts/link/${artifact._id}`)} style="margin-top:10px ;padding:10px " width="80%" alt="Vessel Detection Loading..." />
                                            </td>
                                        </tr>`
            : ""
        }
                                
                                ${mapData ? `<tr>
                                        <td style="text-align:center">
                                            <img src="${mapData}" style="margin-top:10px ;padding:10px " width="80%"  alt="Ship"/>
                                        </td>
                                    </tr>` : ''
        }
                                
                                <tr>
                                    <td>
                                        <p style=" width:90%; margin:auto; padding-top:20px;padding-bottom:10px">
                                            This email originated from ${process.env.NODE_ENV === 'prod' ? 'portal' : process.env.NODE_ENV}.quartermaster.us.
                                            If you don't want to receive further emails please click the button below.
                                        </p>
                                    </td>
                                </tr>
            
            
                                <tr>
                                    <td>
                                        <p> <a style="background-color:#1E293B; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                        " href="${unsubscribeLink}"> Unsubscribe </a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
            
                    <tr>
                        <td>
                            <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">
                            </table>
                        </td>
                    </tr>
            
                    <!-- Footer Section -->
                    <tr>
                        <td>
                            <table class="footer">
                                <tr>
                                    <td>
                                        <p></p>
                                        <p>If you have any questions, feel free to message us at
                                            <strong><EMAIL></strong>. All rights reserved. Update email preferences or unsubscribe.
                                        </p>
                                        <p>
                                            <a href=${terms} style="color: #666666;">
                                                Terms of use
                                            </a> |
                                            <a href=${privacy} style="color: #666666;">
                                                Privacy Policy
                                            </a>
                                        </p>
                                        &copy; 2025 Quartermaster. All Rights Reserved.
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </body>
        </html>
    `
}

// const sampleSummaryData = {
//     type: 'daily',
//     superCtg: '77 Vessels (Cargo : 66 , Passenger : 07 , Special Craft : 04)',
//     flags: '12 Vessels (China : 06 , Philippines : 02 , Unknown : 04) ',
//     highDetection: '02 Detections',
//     incursions: false,
//     totalDetection: 10,
//     link: 'https://quartermaster.us',
//     vessels: [{
//         name: 'BRP 123 Vessel',
//         value: 123
//     }, {
//         name: "BGTR 1232 Sea",
//         value: 100
//     }]

// }


const NOTIFICATION_SUMMARY_EMAIL_CONTENT = (data, date, unsubscribeLink, mapData = undefined) => {
    const vesselRows = data.vessels
        .map(
            (vessel) => `
            <tr>
                <td class="data_left">
                    ${vessel.name}
                </td>
                <td class="data_right">
                    ${vessel.value}
                </td>
            </tr>`
        )
        .join("");

    return `
     <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }

        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .heading_left {
            text-align: left;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            padding-top: 5px;
            padding-bottom: 3px;
            font-size: 20px;
        }

        .heading_right {
            text-align: right;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            font-size: 20px;
        }

        .data_left {
            text-align: left;
            color: #1F1F1F;
            font-size:18px
        }

        .data_right {
            text-align: right;
            color: #1F1F1F;
            font-size:18px
        }
        
        .heading_full_left {
            text-align: left;
            font-weight: bold;
            color: #0285FE;
            width: 100%;
            padding-top: 5px;
            padding-bottom: 3px;
            font-size: 25px;
            margin-top: 10px;
        }

        .heading_full_right {
            text-align: right;
            font-weight: bold;
            color: #0285FE;
            width: 100%;
            font-size: 25px;
        }

        .data_full_left {
            text-align: left;
            color: #1F1F1F;
            font-size:18px;
        }

        .data_full_right {
            text-align: right;
            color: #1F1F1F;
        }

        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 100%;
            }
            .content h1 {
                font-size: 20px;
            }
            .content p {
                font-size: 14px;
            }

            .heading_left {
                font-size: 16px;
            }

            .heading_right {
                font-size: 16px;
            }
            
            .data_left {
                font-size: 14px
            }

            .data_right {
                font-size: 14px
            }
            .heading_full_left {
                font-size: 16px;
            }

            .heading_full_right {
                font-size: 16px;
            }
            .data_full_left {
                font-size: 14px;
            }
            .footer {
                font-size: 10px;
            }
            .date {
                font-size: 14px;
            }
        }
    </style>
    <title>${data.type} Vessel Detection Report</title>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src='https://portal.quartermaster.us/email/header.png' alt="Background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content" style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>


                    <tr>
                        <td>
                            <h1 style="color:#1F1F1F;">${data.type} Vessel Detection Report</h1>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <table style="width: 90%">
                                <tr>
                                    <td class="heading_full_left">
                                        Number of Vessels Detected by Category
                                    </td>
                                 
                                </tr>
                                <tr>
                                    <td class="data_full_left">
                                        ${data.superCtg}
                                    </td>
                                </tr>
                                 <tr>

                                    <td class="heading_full_left">
                                        Number of Vessels Detected by National Flag
                                    </td>
                                 
                                </tr>
                                <tr>
                                    <td class="data_full_left">
                                        ${data.flags}
                                    </td>
                                </tr>
                                
                                ${data.highDetection !== null ? `<tr>
                                        <td class="heading_full_left">
                                            Number of High-Interest Detections
                                        </td>

                                    </tr>
                                    <tr>
                                        <td class="data_full_left">
                                            ${data.highDetection}
                                        </td>
                                    </tr>` : ''
    }
                                
                                ${data.incursions !== null ?
        `<tr>
                                            <td class="heading_full_left">
                                            Possible EEZ Incursions
                                        </td>
                                         
                                        </tr>
                                        <tr>
                                            <td class="data_full_left">
                                                ${data.incursions}
                                            </td>
                                        </tr>` : ''
    }    
                                
                                <tr>

                                    <td class="heading_full_left">
                                        Number of Detections by Sensor
                                    </td>
                                </tr>
                                <tr>
                                    <td class="data_left">
                                      Total Detections
                                    </td>
                                    <td class="data_right">
                                        ${data.totalDetection}
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2"><hr/></td>
                                </tr>
                                
                                 ${vesselRows}
                            </table>
                        </td>
                    </tr>
                    
                    <tr>
                        <td>
                            <p> <a style="background-color:#1E293B; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                    " href=${process.env.APP_URL + '/dashboard/statistics/'}> View in Portal </a>
                        </td>
                    </tr>
                    

<!--                    <tr>-->
<!--                        <td style="text-align:center">-->
<!--                            <img src="https://portal.quartermaster.us/email/ship.png" style="margin-top:10px ;padding:10px " width="80%"  alt="Ship"/>-->

<!--                        </td>-->
<!--                    </tr>-->
                    
                    ${mapData ? `<tr>
                            <td style="text-align:center">
                                <img src="${mapData}" style="margin-top:10px ;padding:10px " width="80%"  alt="Ship"/>
    
                            </td>
                        </tr>` : ''
    }
                    
                    <tr>
                        <td>
                            <p style=" width:90%; margin:auto; padding-top:20px;padding-bottom:10px">
                                This email originated from ${process.env.NODE_ENV === 'prod' ? 'portal' : process.env.NODE_ENV}.quartermaster.us.
                                If you don't want to receive further emails please click the button below.
                            </p>
                        </td>
                    </tr>


                    <tr>
                        <td>
                            <p> <a style="background-color:#1E293B; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                    " href="${unsubscribeLink}"> Unsubscribe </a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">
                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href='https://quartermaster.us/' style="color: #666666;">
                                    Terms of use
                                </a> |
                                <a href='https://quartermaster.us/' style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
    `
}

module.exports = {
    ARTIFACT_NOTIFICATION_EMAIL_CONTENT,
    NOTIFICATION_SUMMARY_EMAIL_CONTENT
}