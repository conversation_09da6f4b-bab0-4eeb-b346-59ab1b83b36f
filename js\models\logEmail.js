const mongoose = require('mongoose');
const db = require("../modules/db");


const logEmailSchema = new mongoose.Schema({
    created_at: {
        type: Date,
        default: () => new Date().toISOString()
    },
    subject: {
        type: String,
        default: 'artifact detected'
    },
    type: {
        type: String,
        required: true,
        enum: ['artifact']
    },
    receivers: [{
        type: String,
        required: true
    }],
    environment: {
        type: String,
        required: true,
        default: () => process.env.NODE_ENV
    },
    data: {
        type: Object,
    },
    delivered: {
        type: Boolean,
        default: false
    },
});

const LogEmail = db.qm.model('LogEmail', logEmailSchema, 'logs_emails');

module.exports = LogEmail;
