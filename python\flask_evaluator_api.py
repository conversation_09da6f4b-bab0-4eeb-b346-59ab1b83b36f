import os
from dotenv import load_dotenv
from flask import Flask, request, jsonify
from flask_cors import CORS
from evaluator import Evaluator
from datetime import datetime

app = Flask(__name__)
CORS(app)
load_dotenv()

if not os.getenv("FLASK_PORT"):
    raise ValueError("FLASK_PORT environment variable is required")
if not os.getenv("FLASK_DEBUG"):
    raise ValueError("FLASK_DEBUG environment variable is required")

evaluator = Evaluator()


def parse_time_diff(t1, t2):
    return abs(
        (
            datetime.fromisoformat(t2.replace("Z", "+00:00"))
            - datetime.fromisoformat(t1.replace("Z", "+00:00"))
        ).total_seconds()
        / 60
    )


def should_skip_evaluation(a1, a2):
    return (
        a1.get("image_path") == a2.get("image_path")
        or parse_time_diff(a1.get("timestamp"), a2.get("timestamp")) > 30
        or a1.get("onboard_vessel_id") != a2.get("onboard_vessel_id")
    )


@app.route("/evaluate/multiple", methods=["POST"])
def evaluate_sorted_artifacts():
    print("evaluate_sorted_artifacts called")
    sorted_artifacts = request.get_json().get("sorted_artifacts")
    print(f"Received {len(sorted_artifacts)} sorted artifacts for evaluation")
    results = []
    for i in range(len(sorted_artifacts) - 1):
        curr, next_art = sorted_artifacts[i], sorted_artifacts[i + 1]

        same_image_path = curr.get("image_path") == next_art.get("image_path")
        time_diff_minutes = parse_time_diff(curr.get("timestamp"), next_art.get("timestamp"))
        same_vessel = curr.get("onboard_vessel_id") == next_art.get("onboard_vessel_id")

        if should_skip_evaluation(curr, next_art):
            results.append({"duplication_index": 0, "_id": next_art.get("_id")})
            print(f"Pair {i}: artifact_id={next_art.get('_id')} duplication_index=0 (same_path={same_image_path}, time_diff={time_diff_minutes:.1f}min>30={time_diff_minutes > 30}, same_vessel={same_vessel})")
        else:
            eval_results = evaluator.evaluate(curr, next_art)
            duplication_index = sum(eval_results.values()) / len(eval_results)
            results.append({"duplication_index": duplication_index, "_id": next_art.get("_id")})
            print(f"Pair {i}: artifact_id={next_art.get('_id')} duplication_index={duplication_index:.3f}")

    print(f"Returning {len(results)} evaluation results")
    return jsonify(results)


@app.route("/evaluate/single", methods=["POST"])
def evaluate_single():
    data = request.get_json()
    prev, curr = data.get("prev_artifact"), data.get("curr_artifact")

    print(f"Evaluating prev: {prev.get('_id')} vs curr: {curr.get('_id')}")

    same_image_path = prev.get("image_path") == curr.get("image_path")
    time_diff_minutes = parse_time_diff(prev.get("timestamp"), curr.get("timestamp"))
    same_vessel = prev.get("onboard_vessel_id") == curr.get("onboard_vessel_id")

    if should_skip_evaluation(prev, curr):
        print(f"duplication_index=0 (same_path={same_image_path}, time_diff={time_diff_minutes:.1f}min>30={time_diff_minutes > 30}, same_vessel={same_vessel})")
        return jsonify({"duplication_index": 0, "_id": curr.get("_id")})

    eval_results = evaluator.evaluate(prev, curr)
    duplication_index = sum(eval_results.values()) / len(eval_results)
    eval_results["duplication_index"] = duplication_index
    eval_results["_id"] = curr.get("_id")
    print(f"duplication_index={duplication_index:.3f}")
    print(f"Returning single evaluation result")
    return jsonify(eval_results)


if __name__ == "__main__":
    host = "0.0.0.0"
    port = int(os.getenv("FLASK_PORT"))
    if os.getenv("FLASK_DEBUG").lower() == "true":
        app.run(
            host=host,
            port=port,
            debug=True,
        )
    else:
        from waitress import serve
        print(" * Serving Flask app 'flask_evaluator_api'")
        print(" * Debug mode: off")
        print(f" * Running on all addresses ({host})")
        print(f" * Running on http://localhost:{port}")
        serve(app, host=host, port=port)

# test python ci