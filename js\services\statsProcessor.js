const StatsProcessor = require('../models/Statistics');
const db = require('../modules/db');
const { createLoggerWithPath } = require('../modules/winston');
const { getSessionsByCoordinates, getPreviousWeekDateTimeUTC, getPastDaylightDateTimeUTC, getDBCollectionNames } = require('../utils/functions');
const { schedule } = require("node-cron");
const HomePort = require('../models/HomePort');
const { postLogToSlack } = require('../modules/notifyLog');

const logger = createLoggerWithPath('aggregate_statistics')

const stationaryDistance = 15; //in meters

async function generateWeeklyStatistics() {
    try {
        const ts = new Date().getTime();

        logger.info('[generateWeeklyStatistics] invoked')

        const { start: startTimestamp, end: endTimestamp } = getPreviousWeekDateTimeUTC()

        const query = {
            artifacts: {
                timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) },
                vessel_presence: true,
                super_category: { $ne: null },
                onboard_vessel_id: { $ne: null }
            },
            locations: {
                timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) },
                onboardVesselId: { $ne: null }
            }
        }
        logger.info(query)

        const artifacts = await db.qmai.collection('analysis_results').find(query.artifacts).toArray();
        const collectionNames = await getDBCollectionNames(db.qmLocations)
        const location_collections = await Promise.all(collectionNames.map(async (collection) => ({ unit_id: collection.replace(/_location$/, ''), locations: await db.qmLocations.collection(collection).find(query.locations, { sort: { timestamp: 1 } }).toArray() })))
        logger.info(`time taken to fetch data ${new Date().getTime() - ts}`)

        const statistics = {
            totalArtifactsWithAtleastOneVessel: {
                confidenceAbove40: 0,
                confidenceAbove80: 0
            },
            totalVesselsSuperCategorized: {},
            totalVesselsSubCategorized: {},
            listOfTextsExtracted: [],
            totalVesselsWithCountryFlag: {},
            totalVesselsDetectedbySensors: {},
            totalVesselsByHoursUTC: Object.fromEntries(Array.from({ length: 24 }).map((_, i) => [i, 0])),
            totalVesselsByWeekDayHoursUTC: {},
            totalSensorsDurationAtSea: {},
            totalSensorsOnlineDuration: {},
            totalSmartmastsDistanceTraveled: {}
        }

        statistics.totalArtifactsWithAtleastOneVessel.confidenceAbove40 = artifacts.filter((a, i, self) => a.det_conf > 0.4 && i === self.findIndex((el) => el.image_path === a.image_path)).length;
        statistics.totalArtifactsWithAtleastOneVessel.confidenceAbove80 = artifacts.filter((a, i, self) => a.det_conf > 0.8 && i === self.findIndex((el) => el.image_path === a.image_path)).length;

        statistics.totalVesselsSuperCategorized = artifacts.reduce((obj, a) => {
            if (!a.super_category || a.det_nbbox_area < 0.03) return obj;
            if (!obj[a.super_category]) obj[a.super_category] = 0;
            obj[a.super_category] += 1;
            return obj;
        }, {});

        statistics.totalVesselsSubCategorized = artifacts.reduce((obj, a) => {
            if (!a.category || a.det_nbbox_area < 0.03) return obj;
            if (!obj[a.category]) obj[a.category] = 0;
            obj[a.category] += 1;
            return obj;
        }, {});

        statistics.listOfTextsExtracted = artifacts.filter(a => a.text_extraction && a.text_extraction.length > 0).map(a => a.text_extraction.map(o => o.text)).flat().filter((t, i, self) => i === self.findIndex((el) => el === t))

        statistics.totalVesselsWithCountryFlag = artifacts.reduce((obj, a) => {
            if (!a.country_flag) return obj;
            if (!obj[a.country_flag]) obj[a.country_flag] = 0;
            obj[a.country_flag] += 1;
            return obj;
        }, {});

        artifacts.forEach(a => {
            const vesselId = a.onboard_vessel_id.toString();
            if (vesselId) {
                if (!statistics.totalVesselsDetectedbySensors[vesselId]) statistics.totalVesselsDetectedbySensors[vesselId] = 0;
                statistics.totalVesselsDetectedbySensors[vesselId] += 1;
            }
        });

        artifacts.forEach((a) => {
            statistics.totalVesselsByHoursUTC[new Date(a.timestamp).getUTCHours()] += 1;
            const dateHour = new Date(new Date(a.timestamp).setMinutes(0, 0, 0)).toISOString();
            if (!statistics.totalVesselsByWeekDayHoursUTC[dateHour]) statistics.totalVesselsByWeekDayHoursUTC[dateHour] = 0;
            statistics.totalVesselsByWeekDayHoursUTC[dateHour] += 1;
            // statistics.totalVesselsByWeekDayHoursUTC[new Date(a.timestamp).toISOString().split('T')[0]][new Date(a.timestamp).getUTCHours()] += 1;
        });

        const LatLonSpherical = (await import('geodesy/latlon-spherical.js')).default;
        const homePorts = await HomePort.find()

        const allVesselLocations = {};
        location_collections.forEach(unit_location => {
            unit_location.locations.forEach(location => {
                if (!location.onboardVesselId) return;
                const vesselId = location.onboardVesselId.toString();
                if (!allVesselLocations[vesselId]) allVesselLocations[vesselId] = [];
                allVesselLocations[vesselId].push(location);
            });
        });

        Object.keys(allVesselLocations).forEach(vesselId => {
            allVesselLocations[vesselId].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        });

        statistics.totalSensorsDurationAtSea = {};
        Object.entries(allVesselLocations).forEach(([vesselId, locations]) => {
            const seaLocations = locations.filter(loc => {
                if (homePorts.some(c => new LatLonSpherical(c.lat, c.lng).distanceTo(new LatLonSpherical(loc.latitude, loc.longitude)) < 1000))
                    return false
                else
                    return true
            })
            const sessions = getSessionsByCoordinates(seaLocations);
            let onlineDuration = 0;
            if (sessions.length !== 0) {
                onlineDuration = sessions.reduce((sum, session) => sum += new Date(session[session.length - 1].timestamp).getTime() - new Date(session[0].timestamp).getTime(), 0);
            }
            statistics.totalSensorsDurationAtSea[vesselId] = onlineDuration;
        })

        Object.entries(allVesselLocations).forEach(([vesselId, locations]) => {
            const sessions = getSessionsByCoordinates(locations);
            let onlineDuration = 0;
            if (sessions.length !== 0) {
                onlineDuration = sessions.reduce((sum, session) => sum += new Date(session[session.length - 1].timestamp).getTime() - new Date(session[0].timestamp).getTime(), 0);
            }
            statistics.totalSensorsOnlineDuration[vesselId] = onlineDuration;
        })

        Object.entries(allVesselLocations).forEach(([vesselId, locations]) => {
            /* excluding the homeports approach */
            // const seaLocations = unit_location.locations.filter(currLoc => {
            //     const nextLoc = unit_location.locations[i + 1]
            //     if ()
            //         if (seaPorts.some(c => new LatLonSpherical(c.lat, c.lng).distanceTo(new LatLonSpherical(loc.latitude, loc.longitude)) < 1000))
            //             return false
            //         else
            //             return true
            // })

            /* excluding stationary coordinates approach */
            // const locations = unit_location.locations.filter((currentCoordinate, i) => {
            //     const previousCoordinate = unit_location.locations[i - 1]
            //     const nextCoordinate = unit_location.locations[i + 1]
            //     const previousDistance = previousCoordinate && new LatLonSpherical(currentCoordinate.latitude, currentCoordinate.longitude).distanceTo(new LatLonSpherical(previousCoordinate.latitude, previousCoordinate.longitude));
            //     const nextDistance = nextCoordinate && new LatLonSpherical(currentCoordinate.latitude, currentCoordinate.longitude).distanceTo(new LatLonSpherical(nextCoordinate.latitude, nextCoordinate.longitude));
            //     if ((!previousCoordinate || previousDistance <= stationaryDistance) && nextDistance >= stationaryDistance) return true;
            //     if (previousDistance >= stationaryDistance && nextDistance >= stationaryDistance) return true;
            //     if (previousDistance >= stationaryDistance && (!nextCoordinate || nextDistance <= stationaryDistance)) return true;
            //     return false
            // })
            let distanceSum = 0;
            if (locations.length >= 2) {
                var totalDistance = 0;
                locations.forEach((loc, i) => {
                    const nextLoc = locations[i + 1];
                    if (nextLoc) {
                        const distance = new LatLonSpherical(loc.latitude, loc.longitude).distanceTo(new LatLonSpherical(nextLoc.latitude, nextLoc.longitude));
                        if (distance >= stationaryDistance) totalDistance += distance;
                    }
                });
                distanceSum = totalDistance;
            }
            statistics.totalSmartmastsDistanceTraveled[vesselId] = distanceSum;
        })

        logger.info(`statistics are ${JSON.stringify(statistics)}`)

        await StatsProcessor.create({
            fromTimestamp: startTimestamp,
            toTimestamp: endTimestamp,
            type: 'weekly',
            stats: statistics
        })

        logger.info('[generateWeeklyStatistics] saved to db')

        postLogToSlack({
            severity: 'info',
            message: `Weekly statistics generated successfully\nTime taken: ${new Date().getTime() - ts}ms`,
            stack: 'N/A'
        })
    } catch (err) {
        logger.error(`[generateWeeklyStatistics] error: ${err.message}`)
        console.error('[generateWeeklyStatistics] error:', err)
        postLogToSlack({
            severity: 'fatal',
            message: 'Weekly statistics generation failed',
            stack: err.stack
        })
    }
}

async function generateDailyStatistics() {
    try {
        const ts = new Date().getTime();

        logger.info('[generateDailyStatistics] invoked')

        const { start: startTimestamp, end: endTimestamp } = getPastDaylightDateTimeUTC()

        const query = {
            artifacts: {
                timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) },
                vessel_presence: true,
                super_category: { $ne: null },
                onboard_vessel_id: { $ne: null }
            },
            locations: {
                timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) },
                onboardVesselId: { $ne: null }
            }
        }
        logger.info(query)

        const artifacts = await db.qmai.collection('analysis_results').find(query.artifacts).toArray();
        const collectionNames = await getDBCollectionNames(db.qmLocations)
        const location_collections = await Promise.all(collectionNames.map(async (collection) => ({ unit_id: collection.replace(/_location$/, ''), locations: await db.qmLocations.collection(collection).find(query.locations, { sort: { timestamp: 1 } }).toArray() })))
        logger.info(`time taken to fetch records ${new Date().getTime() - ts}`)
        logger.info(`total artifacts in db within time range ${artifacts.length}`)

        const statistics = {
            totalVesselsDetected: 0,
            totalVesselsDetectedbySensors: {},
            totalVesselsSuperCategorized: {},
            totalVesselsSubCategorized: {},
            totalSmartmastsAtSea: 0,
            totalSmartmastsOnline: 0
        }

        statistics.totalVesselsDetected = artifacts.length;

        artifacts.forEach(a => {
            const vesselId = a.onboard_vessel_id.toString();
            if (vesselId) {
                if (!statistics.totalVesselsDetectedbySensors[vesselId]) statistics.totalVesselsDetectedbySensors[vesselId] = 0;
                statistics.totalVesselsDetectedbySensors[vesselId] += 1;
            }
        });

        statistics.totalVesselsSuperCategorized = artifacts.reduce((obj, a) => {
            if (!a.super_category || a.det_nbbox_area < 0.03) return obj;
            if (!obj[a.super_category]) obj[a.super_category] = 0;
            obj[a.super_category] += 1;
            return obj;
        }, {});

        statistics.totalVesselsSubCategorized = artifacts.reduce((obj, a) => {
            if (!a.category || a.det_nbbox_area < 0.03) return obj;
            if (!obj[a.category]) obj[a.category] = 0;
            obj[a.category] += 1;
            return obj;
        }, {});

        const LatLonSpherical = (await import('geodesy/latlon-spherical.js')).default;
        const homePorts = await HomePort.find()

        const allVesselLocations = {};
        location_collections.forEach(unit_location => {
            unit_location.locations.forEach(location => {
                if (!location.onboardVesselId) return;
                const vesselId = location.onboardVesselId.toString();
                if (!allVesselLocations[vesselId]) allVesselLocations[vesselId] = [];
                allVesselLocations[vesselId].push(location);
            });
        });

        Object.keys(allVesselLocations).forEach(vesselId => {
            allVesselLocations[vesselId].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        });

        Object.entries(allVesselLocations).forEach(([vesselId, locations]) => {
            const seaLocations = locations.filter(loc => {
                if (homePorts.some(c => new LatLonSpherical(c.lat, c.lng).distanceTo(new LatLonSpherical(loc.latitude, loc.longitude)) < 1000))
                    return false
                else
                    return true
            })

            if (seaLocations.length >= 15) {
                statistics.totalSmartmastsAtSea += 1;
            }
        })

        Object.entries(allVesselLocations).forEach(([vesselId, locations]) => {
            if (locations.length >= 15) {
                statistics.totalSmartmastsOnline += 1;
            }
        })

        logger.info(`statistics are ${JSON.stringify(statistics)}`)

        await StatsProcessor.create({
            fromTimestamp: startTimestamp,
            toTimestamp: endTimestamp,
            type: 'daily',
            stats: statistics
        })

        logger.info('[generateDailyStatistics] saved to db')

        postLogToSlack({
            severity: 'info',
            message: `Daily statistics generated successfully\nTime taken: ${new Date().getTime() - ts}ms`,
            stack: 'N/A'
        })
    } catch (err) {
        logger.error(`[generateDailyStatistics] error: ${err.message}`)
        console.error('[generateDailyStatistics] error:', err)
        postLogToSlack({
            severity: 'fatal',
            message: 'Daily statistics generation failed',
            stack: err.stack
        })
    }
}

logger.info('setting init timers for statistics computation')

schedule('1 0 12 * * *', generateDailyStatistics, { scheduled: true, timezone: 'UTC' }) // at 12:00:01 each day
schedule('1 0 16 * * Sunday', generateWeeklyStatistics, { scheduled: true, timezone: 'UTC' }) // at 16:00:01 each Sunday