require('dotenv').config();
const db = require('../modules/db');
const { ObjectId } = require('mongodb');
const evaluatorApi = require('../modules/evaluatorApi');

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

async function main() {
    writeLine('Fetching artifacts sorted by timestamp...');
    const artifacts = await db.qmai.collection("analysis_results")
        .find({}, { sort: { timestamp: 1 } }).toArray();

    if (artifacts.length < 2) {
        writeLine('Not enough artifacts found (need at least 2). Exiting.');
        return;
    }

    writeLine(`Found ${artifacts.length} artifacts. Grouping by vessel...`);
    const grouped = artifacts.reduce((acc, artifact) => {
        const id = artifact.onboard_vessel_id || 'unknown';
        (acc[id] = acc[id] || []).push(artifact);
        return acc;
    }, {});

    const vesselGroups = Object.entries(grouped);
    const eligibleVessels = vesselGroups.filter(([, artifacts]) => artifacts.length >= 2);

    writeLine(`Found ${vesselGroups.length} vessel groups, ${eligibleVessels.length} eligible for evaluation`);

    let totalUpdated = 0, totalProcessed = 0;
    let processedVessels = 0;

    for (const [vesselId, vesselArtifacts] of eligibleVessels) {
        processedVessels++;
        writeProgress(`Processing vessel ${processedVessels}/${eligibleVessels.length}: ${vesselId} (${vesselArtifacts.length} artifacts)...`);

        const data = await evaluatorApi.evaluateMultiple(vesselArtifacts);

        if (!data || !Array.isArray(data)) {
            writeLine(`Vessel ${vesselId}: API evaluation failed, skipping...`);
            continue;
        }

        const bulkResult = await db.qmai.collection("analysis_results").bulkWrite(
            data.map(({ _id, duplication_index }) => ({
                updateOne: {
                    filter: { _id: typeof _id === 'string' ? new ObjectId(_id) : _id },
                    update: { $set: { 'portal.duplication_index': duplication_index } }
                }
            }))
        );

        totalUpdated += bulkResult.modifiedCount;
        totalProcessed += data.length;

        writeLine(`Vessel ${vesselId}: Updated ${bulkResult.modifiedCount}/${data.length} artifacts`);
    }

    writeLine(`Total: Updated ${totalUpdated}/${totalProcessed} artifacts across ${processedVessels} vessels`);

    writeLine('Checking for artifacts without duplication_index...');
    const updateResult = await db.qmai.collection("analysis_results")
        .updateMany({ 'portal.duplication_index': { $exists: false } }, { $set: { 'portal.duplication_index': 0 } });

    if (updateResult.modifiedCount > 0) {
        writeLine(`Set duplication_index=0 for ${updateResult.modifiedCount} remaining artifacts`);
    } else {
        writeLine('All artifacts already have duplication_index');
    }

    writeLine('Artifact evaluation completed successfully!');
}

db.qmai.once('open', () => main().then(() => process.exit(0)));
db.qmai.on('error', (err) => { console.error(err); process.exit(1); });
