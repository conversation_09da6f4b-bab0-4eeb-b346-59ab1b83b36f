const { createServer } = require('http')
const { Server } = require('socket.io');
const { sendTestNotificationsListener } = require('../services/notificationTesting');
const eventEmitter = require('./eventEmitter');
const thingsboardApi = require('./thingsboardApi');

const server = createServer();
const io = new Server(server, { cors: { origin: '*' } });

io.on("connection", (socket) => {
    console.info(`User connected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);
    socket.on('testing/triggerNotifications', sendTestNotificationsListener)
    socket.on('thingsboard/reset-dashboard', async() => {
        try {
            await thingsboardApi.resetDashboards();
            socket.emit('resetDashboard', { success: true });
        } catch (error) {
            socket.emit('resetDashboard', { success: false, error: error.message });
        }
    });
    socket.on('disconnect', () => {
        console.info(`User disconnected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);
        socket.removeAllListeners()
    });
});

eventEmitter.on('alert/notify', (data) => {
    io.emit('in_app_notification', data);
});

io.listen(process.env.PORT || 5001)

module.exports = io