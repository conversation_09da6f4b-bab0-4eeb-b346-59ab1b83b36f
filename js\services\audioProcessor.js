const AudioFile = require("../models/AudioFile")
const AudioProcessingCache = require("../models/AudioProcessingCache")
const Vessel = require("../models/Vessel")
const { s3 } = require("../modules/awsS3")
const { postLogToSlack } = require("../modules/notifyLog")

const mockBuckets = ['qsx0004']

/**
 * 
 * @param {import('aws-sdk/clients/s3').ListObjectsV2Request} params 
 * @returns {Promise<import('aws-sdk/clients/s3').ListObjectsV2Output[]>}
 */
const getAllObjectsInS3Dir = async (params, NextContinuationToken = null) => {
    try {
        let responses = []
        let limit = 0
        do {
            const response = await s3.listObjectsV2(params).promise()
            // console.log(response)
            responses.push(response)
            NextContinuationToken = response.NextContinuationToken
            limit++;
            // do not fetch more than 5000 objects
            if (limit >= 5) break;
        } while (NextContinuationToken)
        return responses
    } catch {
        console.error('[audioProcessor] rate limited. retrying after 1 sec')
        await new Promise(resolve => setTimeout(resolve, 1000))
        return getAllObjectsInS3Dir(params, NextContinuationToken)
    }
}

const updateCacheForFolder = async ({ bucketName, folderPath, lastReadFilePath }) => {
    await AudioProcessingCache.findOneAndUpdate({ bucket_name: bucketName, folder_path: folderPath }, { $set: { last_read_file_path: lastReadFilePath, update_timestamp: new Date() } }, { upsert: true })
}

const getNewObjectsFromBucket = async (bucketName) => {
    const artifactFolders = (await getAllObjectsInS3Dir({ Bucket: bucketName, Prefix: 'artifacts/', Delimiter: '/' })).flatMap(res => res.CommonPrefixes).filter(v => v !== undefined)
    // console.log('total artifactFolders', artifactFolders.length, bucketName)
    // if (bucketName === 'qsx0004') {
    //     console.log('qsx0004', JSON.stringify(artifactFolders))
    // }
    // return;

    const cachedFolders = await AudioProcessingCache.find({ bucket_name: bucketName })

    const newObjects = (await Promise.all(artifactFolders.map(async (folder) => {
        // console.log('fetching folder', folder.Prefix)
        if (!folder.Prefix) return []

        const lastReadFilePath = cachedFolders.find(cachedFolder => cachedFolder.folder_path === folder.Prefix)?.last_read_file_path

        const objects = (await getAllObjectsInS3Dir({ Bucket: bucketName, Prefix: folder.Prefix + 'audio/', StartAfter: lastReadFilePath })).flatMap(res => res.Contents).filter(v => v !== undefined)

        if (objects.length) console.log('[audioProcessor] fetched', objects.length, 'objects for folder', folder.Prefix)

        const lastObject = objects[objects.length - 1]
        if (lastObject) {
            // console.log('lastObject', lastObject)
            await updateCacheForFolder({ bucketName, folderPath: folder.Prefix, lastReadFilePath: lastObject.Key })
        }

        return objects
    }))).flat()

    return newObjects;
}

/**
 * 
 * @returns {Promise<{name: string, region: string}[]>}
 */
const getAllS3Buckets = async () => {
    const buckets = await s3.listBuckets().promise()
    const bucketsWithRegions = (await Promise.all(buckets.Buckets.map(async bucket => {
        return {
            name: bucket.Name,
            region: (await s3.getBucketLocation({ Bucket: bucket.Name }).promise()).LocationConstraint
        }
    }))).flat().filter(b => b.name && b.region)
    return bucketsWithRegions
}

const getUnitIdFromBucketName = (bucketName) => {
    if (!bucketName || typeof bucketName !== 'string') throw new Error('bucketName is required string')
    const legacy_buckets = [
        { name: 'smartmast-prototype-24-ap', unit_id: 'prototype-24' },
        { name: 'smartmast-prototype-25-ap', unit_id: 'prototype-25' },
        { name: 'smartmast-prototype-32-ap', unit_id: 'prototype-32' },
        { name: 'smartmast-prototype-33-ap', unit_id: 'prototype-33' },
        { name: 'smartmast-prototype-36-ap', unit_id: 'prototype-36' },
        { name: 'smartmast-prototype-37-ap', unit_id: 'prototype-37' },
    ]
    const legacy_bucket = legacy_buckets.find(b => b.name === bucketName)
    if (legacy_bucket) return legacy_bucket.unit_id
    return bucketName.toLowerCase()
}

const getActiveVesselBuckets = async () => {
    const vessels = await Vessel.find()
    // console.log('vessels', vessels)
    // const unit_ids = vessels.filter(vessel => vessel.unit_id).map(vessel => vessel.unit_id.toLowerCase())
    const s3_buckets = await getAllS3Buckets()
    // console.log(s3_buckets)
    // return;
    // return;
    // console.log('s3_buckets', s3_buckets.Buckets.map(bucket => bucket.Name))
    const active_buckets = (await Promise.all(s3_buckets.map(async bucket => {
        const unit_id = getUnitIdFromBucketName(bucket.name)
        // console.log('unit_id', unit_id)
        const vessel = vessels.find(vessel => vessel.unit_id?.toLowerCase() === unit_id)
        if (!vessel) return null;
        return {
            name: bucket.name,
            region: bucket.region,
            unit_id,
            onboard_vessel_id: vessel._id.toString()
        }
    }))).filter(b => b !== null)

    return active_buckets
}

const processAudios = async () => {
    try {
        console.log('[audioProcessor] starting to process audios')
        const ts = new Date().getTime()

        const activeVesselBuckets = await getActiveVesselBuckets()

        const newObjectsPerBucket = await Promise.all(activeVesselBuckets.map(async bucket => {
            const newObjects = await getNewObjectsFromBucket(bucket.name)
            return {
                bucket_name: bucket.name,
                aws_region: bucket.region,
                unit_id: bucket.unit_id,
                onboard_vessel_id: bucket.onboard_vessel_id,
                new_objects: newObjects
            }
        }))

        console.log('[audioProcessor] total newObjectsPerBucket count', newObjectsPerBucket.reduce((acc, bucket) => acc + bucket.new_objects.length, 0))

        await Promise.all(newObjectsPerBucket.filter(data => data.new_objects.length > 0).map(async data => {
            await insertNewAudioFilesToDB({ newObjects: data.new_objects, bucketName: data.bucket_name, awsRegion: data.aws_region, unit_id: data.unit_id, onboard_vessel_id: data.onboard_vessel_id })
        }))

        // console.log('newObjectsPerBucket', newObjectsPerBucket)
        console.log('[audioProcessor] time taken to process new audios', new Date().getTime() - ts, 'ms')
    } catch (error) {
        console.error('[FATAL ERROR] Error processing audio processor', error)
        postLogToSlack({
            severity: 'fatal',
            message: 'Fatal error in audio processor',
            stack: error.stack
        })
    } finally {
        setTimeout(() => {
            processAudios()
        }, 1 * 60 * 1000)
    }
}

const extract2dHostLocation = (metadata) => {
    if (!metadata) return null;

    const host_location = metadata.host_location;
    if (!host_location || typeof host_location !== 'object') return null;

    if (!host_location.valid || !host_location.longitude || !host_location.latitude) {
        console.warn('[audioProcessor] received invalid host_location in metadata:', metadata)
        return null;
    }
    return {
        type: 'Point',
        coordinates: [host_location.longitude, host_location.latitude]
    }
}

/**
 * 
 * @param {{
 *   newObjects: import('aws-sdk').S3.Object[],
 *   bucketName: string,
 *   awsRegion: string,
 *   unit_id: string,
 *   onboard_vessel_id: string
 * }} params
 * @returns {Promise<void>}
 */
const insertNewAudioFilesToDB = async ({ newObjects, bucketName, awsRegion, unit_id, onboard_vessel_id }) => {
    if (!newObjects) throw new Error('newObjects is required')
    if (!bucketName || typeof bucketName !== 'string') throw new Error('bucketName is required string')
    if (!awsRegion || typeof awsRegion !== 'string') throw new Error('awsRegion is required string')
    if (!unit_id || typeof unit_id !== 'string') throw new Error('unit_id is required string')
    if (!onboard_vessel_id || typeof onboard_vessel_id !== 'string') throw new Error('onboard_vessel_id is required string')


    const wavFiles = newObjects.filter(object => object.Key.endsWith('.wav'))
    const jsonFiles = newObjects.filter(object => object.Key.endsWith('.json'))

    console.log('[audioProcessor] inserting new audio files to DB. count', wavFiles.length)

    const records = (await Promise.all(wavFiles.map(async s3Object => {
        const path_without_ext = s3Object.Key.split('.wav')[0]

        const jsonFilePath = jsonFiles.find(jsonFile => jsonFile.Key === path_without_ext + '.json')?.Key
        if (!jsonFilePath) {
            console.warn('[audioProcessor] no json file found for', s3Object.Key)
            return null
        }

        const metadataContent = await s3.getObject({ Bucket: bucketName, Key: jsonFilePath }).promise()
        const metadata = metadataContent ? JSON.parse(metadataContent.Body.toString()) : null

        if (!metadata) {
            console.warn('[audioProcessor] no metadata found for', s3Object.Key)
            return null
        }

        if (!metadata.timestamp) {
            console.warn('[audioProcessor] malformed metadata for', s3Object.Key)
            return null
        }

        return {
            bucket_name: bucketName,
            aws_region: awsRegion,
            host_location: extract2dHostLocation(metadata),
            metadata_path: jsonFiles.find(jsonFile => jsonFile.Key === path_without_ext + '.json').Key,
            audio_path: s3Object.Key,
            frequency: metadata.frequency || null,
            unit_id: unit_id,
            onboard_vessel_id: onboard_vessel_id,
            timestamp: new Date(metadata.timestamp),
            metadata
        }
    }))).filter(r => r !== null)

    const res = await AudioFile.insertMany(records, { ordered: false })
    console.log('[audioProcessor] inserted', res.length, 'audio files to DB')
}

processAudios()