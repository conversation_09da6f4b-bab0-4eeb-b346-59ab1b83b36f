require('dotenv').config();
const db = require('../modules/db');
const fs = require('fs');
const path = require('path');
const { getTerminalInput, getTerminalConfirmation } = require('../utils/functions');

/**
 * note 1: first you have to remove and re-add the timeseries collection properly
 * note 2: you have to manually change db name to 'locations' in db.js. and you must have db url .env set to user with write privileges to that db.
 */

const tempDir = path.join(__dirname, './temp');

async function fixStationaryCoords() {
    const collection = await getTerminalInput('Enter collection name: ');
    const all_messages = await db.qmLocations.collection(collection).find().toArray();
    console.log('all_messages', all_messages.length)

    const newMessages = all_messages.map(message => {
        return {
            ...message,
            isStationary: false
        }
    })


    // save backup
    fs.writeFileSync(path.join(tempDir, `${collection}.json`), JSON.stringify(all_messages), { flag: 'w' })

    console.log('back saved at', path.join(tempDir, `${collection}.json`))

    // console.log('newMessages', newMessages)

    const confirmation = await getTerminalConfirmation(`Removing and creating new documents. length = ${newMessages.length}. Continue? (y/n): `);

    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    await db.qmLocations.collection(collection).deleteMany({})
    await db.qmLocations.collection(collection).insertMany(newMessages)

    console.log('Success')
    process.exit(0)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qmLocations.once('open', resolve);
        db.qmLocations.on('error', reject);
    })
]).then(() => {
    fixStationaryCoords()
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});