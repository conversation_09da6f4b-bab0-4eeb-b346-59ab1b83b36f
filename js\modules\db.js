const mongoose = require('mongoose');

mongoose.set('strictQuery', false)

const db = {
    qm: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'quartermaster' }),
    qmai: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'artifact_processor' }),
    qmShared: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'quartermaster-shared' }),
    qmLocations: mongoose.createConnection(process.env.MONGO_URI, { dbName: process.env.NODE_ENV === 'prod' ? 'locations' : 'locations-local' }),
    qmAis: mongoose.createConnection(process.env.MONGO_URI, { dbName: process.env.NODE_ENV === 'prod' ? 'ais' : 'ais-local' }),
    audio: mongoose.createConnection(process.env.MONGO_URI, { dbName: process.env.NODE_ENV === 'prod' ? 'audio_processor' : 'audio_processor-local' }),
    lookups: mongoose.createConnection(process.env.MONGO_URI, { dbName: process.env.NODE_ENV === 'prod' ? 'lookups' : 'lookups-local' }),
    locationsOptimized: mongoose.createConnection(process.env.MONGO_URI, { dbName: process.env.NODE_ENV === 'prod' ? 'locations_optimized' : 'locations_optimized-local' }),
    locationsRaw: mongoose.createConnection(process.env.MONGO_URI, { dbName: process.env.NODE_ENV === 'prod' ? 'locations_raw' : 'locations_raw-local' })
}

db.qm.on('open', () => console.log('DB connected to Quartermaster'))
db.qmai.on('open', () => console.log('DB connected to QMAI'))
db.qmShared.on('open', () => console.log('DB connected to Quartermaster-Shared'));
db.qmLocations.on('open', () => console.log('DB connected to Locations'));
db.qmAis.on('open', () => console.log('DB connected to AIS'));
db.audio.on('open', () => console.log('DB connected to Audio'));
db.lookups.on('open', () => console.log('DB connected to Lookups'));
db.locationsOptimized.on('open', () => console.log('DB connected to Location-Optimized'));
db.locationsRaw.on('open', () => console.log('DB connected to Location-Raw'));


db.qm.on('error', (err) => console.error(err))
db.qmai.on('error', (err) => console.error(err))
db.qmShared.on('error', (err) => console.error(err));
db.qmLocations.on('error', (err) => console.error(err));
db.qmAis.on('error', (err) => console.error(err));
db.audio.on('error', (err) => console.error(err));
db.lookups.on('error', (err) => console.error(err));
db.locationsOptimized.on('error', (err) => console.error(err));
db.locationsRaw.on('error', (err) => console.error(err));

module.exports = db