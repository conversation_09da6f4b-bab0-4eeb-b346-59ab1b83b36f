
require('dotenv').config()

const fs = require('fs')
const db = require('../modules/db');
const { getTerminalConfirmation } = require('../utils/functions');

const readInput = () => {
    return new Promise((resolve) => {
        process.stdout.write('Input name of AIS collection: ');
        process.stdin.on('data', (data) => {
            const input = data.toString().trim();
            resolve(input);
        });
    });
}

async function updateIdInAisData() {

    const collectionName = await readInput()
    if (!(await db.qmAis.listCollections()).map(c => c.name).includes(collectionName)) {
        throw new Error('Collection not found')
    }

    const collection = db.qmAis.collection(collectionName)

    const oldData = await collection.find().toArray()

    const newData = oldData.map((item) => {

        return {
            ...item,
            metadata: {
                ...item.metadata,
                _id: item._id
            }
        }
    })

    console.log('newData', newData)

    const confirmation = await getTerminalConfirmation(`Clearning and inserting ${newData.length} reecords in environment ${process.env.NODE_ENV} in collection ${collection.name}, continue? (y/n): `);

    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    await collection.deleteMany({})
    await collection.insertMany(newData)

    console.log('Success')
    process.exit(0)
}

db.qmAis.on('open', () => {
    console.log('DB connected to AIS');
    updateIdInAisData();
})