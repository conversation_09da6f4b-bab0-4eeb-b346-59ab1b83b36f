const axios = require('axios');

const ENVIRONMENT = process.env.NODE_ENV;
const SLACK_WEBHOOK_URL = process.env.SLACK_LOGS_WEBHOOK_URL;

if (!SLACK_WEBHOOK_URL) throw new Error('[notifyLog.js] SLACK_WEBHOOK_URL is missing in the .env');
if (!ENVIRONMENT) throw new Error('[notifyLog.js] ENVIRONMENT is missing in the .env');

/**
 * Generate a template for a slack notification
 * @param {Object} param0 
 * @param {'fatal' | 'error' | 'warning' | 'info'} param0.severity 
 * @param {String} param0.message 
 * @param {String} param0.stack 
 * @returns 
 */
const slackTemplate = ({ severity, message, stack }) => (
    {
        "blocks": [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": `${severity === 'error' ? 'Error detected' : severity === 'fatal' ? 'FATAL error detected' : severity === 'warning' ? 'Warning message' : 'Info message'} in microservices`,
                    "emoji": true
                }
            },
            {
                "type": "divider"
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": `*Environment:*\n${ENVIRONMENT}`
                    },
                    {
                        "type": "mrkdwn",
                        "text": `*Severity:*\n${severity === 'fatal' ? ':rotating_light:' : severity === 'error' ? ':exclamation:' : severity === 'warning' ? ':warning:' : ':information_source:'} *${severity.toUpperCase()}*`
                    },
                    {
                        "type": "mrkdwn",
                        "text": `*Timestamp (UTC):*\n${new Date().toISOString()}`
                    }
                ]
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": `*Message:*\n${message}`
                }
            },
            {
                "type": "divider"
            },
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": `*Stack trace:*\n${stack}`
                    }
                ]
            }
        ]
    }
)

/**
 * 
 * @param {Object} param0 
 * @param {'fatal' | 'error' | 'warning' | 'info'} param0.severity 
 * @param {String} param0.message 
 * @param {String} param0.stack 
 * @returns 
 */
const postLogToSlack = async ({ severity, message, stack }) => {
    if (ENVIRONMENT !== 'prod') return console.warn('[postLogToSlack] Not in production environment, skipping log to Slack');
    if (!severity || !message || !stack) console.warn('[postLogToSlack] severity, message, stack are required');

    const body = slackTemplate({
        severity: severity || 'unknown',
        message: message || 'Unknown',
        stack: stack || 'Unknown'
    });
    await axios.post(SLACK_WEBHOOK_URL, body)
    console.log('[postLogToSlack] Log sent to Slack')

    return;
}

module.exports = {
    postLogToSlack
}