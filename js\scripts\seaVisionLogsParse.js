const fs = require('fs');
const BSON = require('bson'); // note: no instantiation needed

const bsonFilePath = '/Users/<USER>/Documents/GitHub/quartermaster-web-microservices/scripts/temp/logs_sea_vision.bson';

let leftover = Buffer.alloc(0);
let documentCount = 0;

const stream = fs.createReadStream(bsonFilePath);

const newLogs = []

stream.on('data', (chunk) => {
    let buffer = Buffer.concat([leftover, chunk]);

    while (buffer.length >= 4) {
        const docSize = buffer.readInt32LE(0);
        if (buffer.length < docSize) break;

        const docBuffer = buffer.slice(0, docSize);
        try {
            const doc = BSON.deserialize(docBuffer); // ✅ use static method
            documentCount++;

            delete doc.submitted_data.file;

            newLogs.push(doc);

            // Process the document here
            console.log(`Doc #${documentCount}:`, doc); // Optional
        } catch (e) {
            console.error('Failed to parse document:', e);
            break;
        }

        buffer = buffer.slice(docSize);
    }

    leftover = buffer;
});

stream.on('end', () => {
    console.log(`Finished. Parsed ${documentCount} documents.`);
});

stream.on('error', (err) => {
    console.error('Stream error:', err);
    fs.writeFileSync('newLogs.json', JSON.stringify(newLogs, null, 2));
});

setInterval(() => {
    console.log(`Saving ${newLogs.length} logs`);
    fs.writeFileSync('temp/newSeaVisionLogs.json', JSON.stringify(newLogs, null, 2));
    console.log(`Saved ${newLogs.length} logs`);
}, 5000);
