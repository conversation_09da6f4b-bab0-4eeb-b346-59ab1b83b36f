const mongoose = require('mongoose');
const db = require('../modules/db');

const audioProcessingCacheSchema = new mongoose.Schema({
    bucket_name: {
        type: String,
        required: true,
    },
    folder_path: {
        type: String,
        required: true,
    },
    last_read_file_path: {
        type: String,
        required: true,
    },
    creation_timestamp: {
        type: Date,
        required: false,
        default: () => new Date(),
    },
    update_timestamp: {
        type: Date,
        required: false,
        default: () => new Date(),
    },
});

audioProcessingCacheSchema.index({ bucket_name: 1, folder_path: 1 }, { unique: true });

const AudioProcessingCache = db.audio.model('AudioProcessingCache', audioProcessingCacheSchema, 'audio_processing_cache');

module.exports = AudioProcessingCache;
