const mongoose = require('mongoose');
const db = require('../modules/db');

const audioFileSchema = new mongoose.Schema({
    bucket_name: { type: String, required: true, },
    aws_region: { type: String, required: true, },
    host_location: {
        type: {
            type: String,
            enum: ['Point'],
            required: false
        },
        coordinates: {
            type: [Number],
            required: false
        }
    },
    metadata_path: { type: String, required: true, unique: true },
    audio_path: { type: String, required: true, unique: true },
    unit_id: { type: String, required: true, },
    frequency: { type: String, required: true, },
    onboard_vessel_id: { type: mongoose.Schema.Types.ObjectId, required: false, default: null },
    metadata: { type: Object, required: true },
    timestamp: { type: Date, required: true, index: true },
    creation_timestamp: { type: Date, required: false, default: () => new Date() }
});

const AudioFile = db.audio.model('AudioFile', audioFileSchema, 'audio_files');

module.exports = AudioFile;
