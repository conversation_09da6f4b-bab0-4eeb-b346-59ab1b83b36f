import numpy as np
from sentence_transformers import SentenceTransformer, util
from typing import Dict, Any, List, Union

class Evaluator:
    """
    The Evaluator class is responsible for evaluating the performance of LLM clients
    using a suite of specialized functions for different data types, with robust
    handling of None values.
    """

    def __init__(self):
        """Initializes the Evaluator and the SentenceTransformer model."""
        self.model = SentenceTransformer('all-MiniLM-L6-v2')

    def _eval_exact_match(self, pred_val: Any, gt_val: Any) -> float:
        """
        Computes an exact match score for non-None boolean or categorical string values.
        """
        if isinstance(gt_val, bool) or str(gt_val).lower() in ["true", "false"]:
            gt_bool = str(gt_val).lower() == "true"
            pred_bool = str(pred_val).lower() == "true"
            return 1.0 if gt_bool == pred_bool else 0.0

        gt_str = str(gt_val).lower().strip()
        pred_str = str(pred_val).lower().strip()
        return 1.0 if gt_str == pred_str else 0.0

    def _compute_similarity(self, pred_str: str, gt_str: str) -> float:
        """
        Computes semantic similarity between two non-empty strings.
        """
        embeddings = self.model.encode([pred_str, gt_str], convert_to_tensor=True)
        similarity = util.pytorch_cos_sim(embeddings[0], embeddings[1])
        return max(0.0, similarity.item()) # Clamp at 0

    def _eval_text_recognition(self, pred_list: List[Dict], gt_val: Union[List[Dict], str]) -> float:
        """
        Evaluates the semantic similarity of the recognized text.
        This function is called only when both prediction and ground truth have text.
        """
        if isinstance(gt_val, str):
            gt_texts = gt_val.lower().strip()
        else:
            gt_texts = " ".join(sorted([item.get('text', '').lower() for item in gt_val])).strip()
        
        pred_texts = " ".join(sorted([item.get('text', '').lower() for item in pred_list])).strip()
        
        return self._compute_similarity(pred_texts, gt_texts)
    
    def _evaluate_field(self, pred_val: Any, gt_val: Any, eval_func) -> float:
        """
        A robust wrapper that handles None cases before dispatching to an evaluation function.
        """
        is_pred_empty = pred_val is None or (isinstance(pred_val, (str, list)) and not pred_val)
        is_gt_empty = gt_val is None or (isinstance(gt_val, (str, list)) and not gt_val)

        if is_pred_empty and is_gt_empty:
            return 1.0
        
        if is_pred_empty or is_gt_empty:
            return 0.0
            
        return eval_func(pred_val, gt_val)

    def evaluate(self, predictions: Dict[str, Any], ground_truth: Dict[str, Any]) -> Dict[str, float]:
        """
        Evaluate client predictions against ground truth data using field-specific functions
        with robust None handling.
        """
        results = {}
        
        field_configs = {
            "vessel_presence": self._eval_exact_match,
            "category": self._compute_similarity,
            "super_category": self._eval_exact_match,
            "size": self._compute_similarity,
            "imo_number": self._compute_similarity,
            "color": self._compute_similarity,
            "weapons": self._compute_similarity,
            "country_flag": self._compute_similarity,
            "others": self._compute_similarity
        }

        for field, eval_func in field_configs.items():
            if field in ground_truth:
                pred_val = predictions.get(field)
                gt_val = ground_truth.get(field)
                results[field] = self._evaluate_field(pred_val, gt_val, eval_func)
        
        if "text_extraction" in ground_truth:
            pred_text_list = predictions.get("text_extraction")
            gt_text_val = ground_truth.get("text_extraction")

            is_pred_empty = not pred_text_list
            is_gt_empty = not gt_text_val or (isinstance(gt_text_val, list) and not gt_text_val)
            
            results["text_detection_accuracy"] = 1.0 if is_pred_empty == is_gt_empty else 0.0

            if not is_pred_empty and not is_gt_empty:
                results["text_recognition_similarity"] = self._eval_text_recognition(pred_text_list, gt_text_val)
            else:
                results["text_recognition_similarity"] = results["text_detection_accuracy"]
            
        return results